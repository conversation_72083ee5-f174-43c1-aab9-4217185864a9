<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي - نظام إدارة مكان المذاكرة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            color: #333;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .demo-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .demo-header p {
            font-size: 1.2rem;
            color: #666;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-10px);
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        .demo-actions {
            text-align: center;
            margin: 40px 0;
        }
        .demo-btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
        }
        .demo-btn.secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }
        .demo-btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(245, 87, 108, 0.6);
        }
        .screenshots {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .screenshot h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .screenshot-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f1f3f4, #e8eaed);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        .tech-specs {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 40px 0;
        }
        .tech-specs h2 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
        }
        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .spec-item {
            text-align: center;
            padding: 15px;
        }
        .spec-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        .spec-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .spec-description {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-book-open"></i> نظام إدارة مكان المذاكرة</h1>
            <p>حل شامل ومتطور لإدارة أماكن المذاكرة والدراسة</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">⏱️</div>
                <div class="feature-title">تايمر مرئي في الوقت الفعلي</div>
                <div class="feature-description">
                    تايمر يعمل بالثواني لكل طالب يذاكر، مع عرض الوقت والتكلفة المحدثة باستمرار
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🧾</div>
                <div class="feature-title">إنشاء فاتورة فورية</div>
                <div class="feature-description">
                    إمكانية إنشاء فاتورة في أي وقت بدون إنهاء جلسة المذاكرة، مع طباعة احترافية
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👨‍🎓</div>
                <div class="feature-title">إدارة الطلاب</div>
                <div class="feature-description">
                    تتبع الطلاب النشطين والمكتملين مع إمكانية تعديل الوقت يدوياً وإضافة المشتريات
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🛒</div>
                <div class="feature-title">إدارة المنتجات</div>
                <div class="feature-description">
                    مشروبات، وجبات خفيفة، قرطاسية وأدوات دراسية مع نظام تكويد وأسعار
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">تقارير شاملة</div>
                <div class="feature-description">
                    تقارير يومية وأسبوعية وشهرية مع إحصائيات مفصلة عن الإيرادات والأداء
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💾</div>
                <div class="feature-title">يعمل بدون إنترنت</div>
                <div class="feature-description">
                    جميع البيانات محفوظة محلياً، لا يحتاج اتصال إنترنت أو سيرفر خارجي
                </div>
            </div>
        </div>

        <div class="screenshots">
            <div class="screenshot">
                <h3>لوحة التحكم</h3>
                <div class="screenshot-placeholder">
                    <i class="fas fa-chart-line fa-3x"></i>
                </div>
                <p>عرض شامل للإحصائيات والطلاب النشطين</p>
            </div>

            <div class="screenshot">
                <h3>إدارة الطلاب</h3>
                <div class="screenshot-placeholder">
                    <i class="fas fa-users fa-3x"></i>
                </div>
                <p>تايمر مرئي وإدارة كاملة للطلاب</p>
            </div>

            <div class="screenshot">
                <h3>نظام الفواتير</h3>
                <div class="screenshot-placeholder">
                    <i class="fas fa-file-invoice fa-3x"></i>
                </div>
                <p>فواتير احترافية قابلة للطباعة</p>
            </div>

            <div class="screenshot">
                <h3>التقارير</h3>
                <div class="screenshot-placeholder">
                    <i class="fas fa-chart-pie fa-3x"></i>
                </div>
                <p>تقارير مفصلة وإحصائيات دقيقة</p>
            </div>
        </div>

        <div class="tech-specs">
            <h2>المواصفات التقنية</h2>
            <div class="specs-grid">
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-globe"></i></div>
                    <div class="spec-title">يعمل في المتصفح</div>
                    <div class="spec-description">لا يحتاج تثبيت برامج</div>
                </div>
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-mobile-alt"></i></div>
                    <div class="spec-title">تصميم متجاوب</div>
                    <div class="spec-description">يعمل على جميع الأجهزة</div>
                </div>
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-moon"></i></div>
                    <div class="spec-title">وضع ليلي</div>
                    <div class="spec-description">راحة للعينين</div>
                </div>
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-print"></i></div>
                    <div class="spec-title">طباعة احترافية</div>
                    <div class="spec-description">فواتير وتقارير جاهزة</div>
                </div>
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-search"></i></div>
                    <div class="spec-title">بحث شامل</div>
                    <div class="spec-description">في جميع البيانات</div>
                </div>
                <div class="spec-item">
                    <div class="spec-icon"><i class="fas fa-shield-alt"></i></div>
                    <div class="spec-title">آمن ومحمي</div>
                    <div class="spec-description">بيانات محلية فقط</div>
                </div>
            </div>
        </div>

        <div class="demo-actions">
            <a href="index.html" class="demo-btn">
                <i class="fas fa-rocket"></i> تشغيل النظام
            </a>
            <a href="test.html" class="demo-btn secondary">
                <i class="fas fa-vial"></i> اختبار سريع
            </a>
            <a href="test-suite.html" class="demo-btn">
                <i class="fas fa-cogs"></i> اختبار شامل
            </a>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #e9ecef;">
            <h3 style="color: #667eea;">🎯 جاهز للاستخدام الفوري</h3>
            <p style="color: #666; font-size: 1.1rem;">
                لا يتطلب تثبيت أو إعداد معقد - فقط افتح الملف في المتصفح وابدأ!
            </p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Initially hide cards
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click effects to buttons
            document.querySelectorAll('.demo-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255,255,255,0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => ripple.remove(), 600);
                });
            });
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
