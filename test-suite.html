<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة اختبارات نظام إدارة المقهى</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ddd;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .test-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.warning {
            background: #f39c12;
        }
        .test-button.danger {
            background: #e74c3c;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #ecf0f1;
            min-height: 40px;
        }
        .success {
            background: #d5f4e6;
            color: #27ae60;
            border-left: 4px solid #27ae60;
        }
        .error {
            background: #fadbd8;
            color: #e74c3c;
            border-left: 4px solid #e74c3c;
        }
        .warning {
            background: #fef9e7;
            color: #f39c12;
            border-left: 4px solid #f39c12;
        }
        .info {
            background: #d6eaf8;
            color: #3498db;
            border-left: 4px solid #3498db;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .run-all-btn {
            background: #e74c3c;
            font-size: 1.2rem;
            padding: 15px 30px;
            margin: 20px auto;
            display: block;
        }
        .test-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 مجموعة اختبارات نظام إدارة المقهى</h1>
            <p>اختبار شامل لجميع وظائف النظام والتأكد من جودة الأداء</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">اختبارات ناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">اختبارات فاشلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testProgress">0%</div>
                    <div class="stat-label">نسبة الإنجاز</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill">0%</div>
            </div>
            
            <button class="test-button run-all-btn" onclick="runAllTests()">
                🚀 تشغيل جميع الاختبارات
            </button>
        </div>

        <!-- Storage Tests -->
        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبارات التخزين المحلي</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>اختبار LocalStorage</h4>
                    <button class="test-button" onclick="testLocalStorage()">تشغيل</button>
                    <div id="localStorageResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>اختبار IndexedDB</h4>
                    <button class="test-button" onclick="testIndexedDB()">تشغيل</button>
                    <div id="indexedDBResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>اختبار النسخ الاحتياطي</h4>
                    <button class="test-button" onclick="testBackup()">تشغيل</button>
                    <div id="backupResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Performance Tests -->
        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> اختبارات الأداء</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>سرعة التحميل</h4>
                    <button class="test-button" onclick="testLoadSpeed()">تشغيل</button>
                    <div id="loadSpeedResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>استهلاك الذاكرة</h4>
                    <button class="test-button" onclick="testMemoryUsage()">تشغيل</button>
                    <div id="memoryResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>اختبار الحمولة</h4>
                    <button class="test-button" onclick="testLoadCapacity()">تشغيل</button>
                    <div id="loadCapacityResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Functionality Tests -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبارات الوظائف</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>إدارة العملاء</h4>
                    <button class="test-button" onclick="testCustomerManagement()">تشغيل</button>
                    <div id="customerResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>إدارة المنتجات</h4>
                    <button class="test-button" onclick="testProductManagement()">تشغيل</button>
                    <div id="productResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>نظام الفواتير</h4>
                    <button class="test-button" onclick="testInvoiceSystem()">تشغيل</button>
                    <div id="invoiceResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>إدارة الشيفتات</h4>
                    <button class="test-button" onclick="testShiftManagement()">تشغيل</button>
                    <div id="shiftResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>نظام التقارير</h4>
                    <button class="test-button" onclick="testReportSystem()">تشغيل</button>
                    <div id="reportResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>البحث الشامل</h4>
                    <button class="test-button" onclick="testGlobalSearch()">تشغيل</button>
                    <div id="searchResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- UI/UX Tests -->
        <div class="test-section">
            <h3><i class="fas fa-paint-brush"></i> اختبارات واجهة المستخدم</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>التصميم المتجاوب</h4>
                    <button class="test-button" onclick="testResponsiveDesign()">تشغيل</button>
                    <div id="responsiveResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>الوضع الليلي</h4>
                    <button class="test-button" onclick="testDarkMode()">تشغيل</button>
                    <div id="darkModeResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>اختصارات لوحة المفاتيح</h4>
                    <button class="test-button" onclick="testKeyboardShortcuts()">تشغيل</button>
                    <div id="keyboardResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Security Tests -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> اختبارات الأمان</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>حماية البيانات</h4>
                    <button class="test-button" onclick="testDataSecurity()">تشغيل</button>
                    <div id="securityResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>صلاحيات المستخدمين</h4>
                    <button class="test-button" onclick="testUserPermissions()">تشغيل</button>
                    <div id="permissionsResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>التحقق من صحة البيانات</h4>
                    <button class="test-button" onclick="testDataValidation()">تشغيل</button>
                    <div id="validationResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Browser Compatibility -->
        <div class="test-section">
            <h3><i class="fas fa-globe"></i> اختبارات توافق المتصفحات</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>ميزات HTML5</h4>
                    <button class="test-button" onclick="testHTML5Features()">تشغيل</button>
                    <div id="html5Result" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>ميزات CSS3</h4>
                    <button class="test-button" onclick="testCSS3Features()">تشغيل</button>
                    <div id="css3Result" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h4>ميزات JavaScript ES6+</h4>
                    <button class="test-button" onclick="testES6Features()">تشغيل</button>
                    <div id="es6Result" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> سجل الاختبارات</h3>
            <div class="test-log" id="testLog">
                جاهز لبدء الاختبارات...
            </div>
        </div>

        <!-- Final Actions -->
        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> الإجراءات النهائية</h3>
            <div style="text-align: center;">
                <button class="test-button success" onclick="generateTestReport()">
                    📊 إنشاء تقرير الاختبارات
                </button>
                <button class="test-button warning" onclick="clearTestData()">
                    🗑️ مسح بيانات الاختبار
                </button>
                <button class="test-button" onclick="openMainApp()">
                    🎯 فتح التطبيق الرئيسي
                </button>
            </div>
        </div>
    </div>

    <script src="js/test-suite.js"></script>
</body>
</html>
