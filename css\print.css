/* Print Styles */
@media print {
    /* Hide unnecessary elements */
    .header,
    .nav,
    .btn,
    .modal-overlay,
    .search-bar,
    .quick-actions,
    .action-buttons,
    .card-actions {
        display: none !important;
    }

    /* Reset page margins */
    @page {
        margin: 1cm;
        size: A4;
    }

    /* Body styles for print */
    body {
        font-family: 'Arial', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    /* Invoice specific styles */
    .invoice-print {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
        background: white;
        box-shadow: none;
        border: none;
    }

    .invoice-header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
    }

    .invoice-header h1 {
        font-size: 18pt;
        margin-bottom: 5px;
        color: #000;
    }

    .invoice-header p {
        font-size: 10pt;
        margin: 2px 0;
        color: #333;
    }

    .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        font-size: 11pt;
    }

    .invoice-details div {
        flex: 1;
    }

    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
        font-size: 10pt;
    }

    .invoice-table th,
    .invoice-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: right;
    }

    .invoice-table th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    .invoice-table .text-center {
        text-align: center;
    }

    .invoice-table .text-left {
        text-align: left;
    }

    .invoice-summary {
        margin-top: 20px;
        text-align: right;
        font-size: 11pt;
    }

    .invoice-summary .total-row {
        font-weight: bold;
        font-size: 12pt;
        border-top: 2px solid #000;
        padding-top: 5px;
    }

    .invoice-footer {
        margin-top: 30px;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 10px;
    }

    /* Receipt styles for thermal printers */
    .receipt-print {
        width: 58mm;
        font-family: 'Courier New', monospace;
        font-size: 8pt;
        line-height: 1.2;
        margin: 0;
        padding: 5px;
    }

    .receipt-print .receipt-header {
        text-align: center;
        margin-bottom: 10px;
        border-bottom: 1px dashed #000;
        padding-bottom: 5px;
    }

    .receipt-print .receipt-header h2 {
        font-size: 10pt;
        margin: 0;
        font-weight: bold;
    }

    .receipt-print .receipt-line {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2px;
        font-size: 7pt;
    }

    .receipt-print .receipt-total {
        border-top: 1px dashed #000;
        padding-top: 5px;
        margin-top: 5px;
        font-weight: bold;
        text-align: center;
    }

    .receipt-print .receipt-footer {
        text-align: center;
        margin-top: 10px;
        font-size: 6pt;
        border-top: 1px dashed #000;
        padding-top: 5px;
    }

    /* Report print styles */
    .report-print {
        width: 100%;
    }

    .report-print h1,
    .report-print h2,
    .report-print h3 {
        color: #000;
        margin-bottom: 10px;
    }

    .report-print .report-section {
        margin-bottom: 20px;
        page-break-inside: avoid;
    }

    .report-print table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15px;
        font-size: 9pt;
    }

    .report-print table th,
    .report-print table td {
        border: 1px solid #000;
        padding: 5px;
        text-align: right;
    }

    .report-print table th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    /* Chart print styles */
    .chart-print {
        width: 100%;
        height: auto;
        margin-bottom: 20px;
    }

    /* Force page breaks */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* Hide interactive elements */
    button,
    input[type="button"],
    input[type="submit"],
    .interactive {
        display: none !important;
    }

    /* Ensure text is black */
    * {
        color: #000 !important;
        background: transparent !important;
    }

    /* Table improvements */
    table {
        border-collapse: collapse !important;
    }

    th, td {
        border: 1px solid #000 !important;
    }

    /* Typography improvements */
    h1, h2, h3, h4, h5, h6 {
        font-weight: bold !important;
        margin-bottom: 10px !important;
    }

    p {
        margin-bottom: 5px !important;
    }

    /* Spacing improvements */
    .print-section {
        margin-bottom: 20px !important;
    }

    .print-header {
        margin-bottom: 15px !important;
    }

    .print-footer {
        margin-top: 15px !important;
    }
}

/* Print utilities */
.print-only {
    display: none;
}

@media print {
    .print-only {
        display: block !important;
    }
    
    .no-print {
        display: none !important;
    }
}
