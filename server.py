#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سيرفر محلي بسيط لنظام إدارة مكان المذاكرة
يحل مشكلة حفظ البيانات في المتصفح
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import json
from urllib.parse import urlparse, parse_qs
import threading
import time

class StudyPlaceHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # إضافة headers للسماح بحفظ البيانات
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # تخصيص رسائل السجل
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def find_free_port(start_port=8000, max_attempts=100):
    """البحث عن منفذ متاح"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"لم يتم العثور على منفذ متاح في النطاق {start_port}-{start_port + max_attempts}")

def create_backup_system():
    """إنشاء نظام النسخ الاحتياطي"""
    backup_dir = "backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"✅ تم إنشاء مجلد النسخ الاحتياطي: {backup_dir}")

    # إنشاء ملف تكوين للنسخ الاحتياطي
    backup_config = {
        "auto_backup": True,
        "backup_interval_minutes": 30,
        "max_backups": 10,
        "backup_location": backup_dir,
        "created_at": time.strftime('%Y-%m-%d %H:%M:%S'),
        "version": "1.0"
    }

    config_file = os.path.join(backup_dir, "config.json")
    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(backup_config, f, ensure_ascii=False, indent=2)

    # إنشاء ملف README للنسخ الاحتياطي
    readme_content = """# مجلد النسخ الاحتياطي

هذا المجلد يحتوي على النسخ الاحتياطية التلقائية لبيانات النظام.

## الملفات:
- config.json: إعدادات النسخ الاحتياطي
- backup_YYYYMMDD_HHMMSS.json: ملفات النسخ الاحتياطي

## ملاحظات:
- يتم إنشاء نسخة احتياطية كل 30 دقيقة تلقائياً
- يتم الاحتفاظ بآخر 10 نسخ احتياطية
- لا تحذف هذا المجلد أو محتوياته
"""

    readme_file = os.path.join(backup_dir, "README.md")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(readme_content)

def check_requirements():
    """فحص المتطلبات"""
    required_files = [
        "index.html",
        "js/app.js",
        "js/storage.js",
        "css/styles.css"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def open_browser_delayed(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        time.sleep(delay)
        print(f"🌐 فتح المتصفح: {url}")
        webbrowser.open(url)
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🎓 نظام إدارة مكان المذاكرة                    ║
║                      Study Place Management                  ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 سيرفر محلي لحفظ البيانات بأمان                              ║
║  💾 نسخ احتياطي تلقائي                                        ║
║  🔒 حماية من فقدان البيانات                                    ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ يرجى التأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء نظام النسخ الاحتياطي
    create_backup_system()
    
    try:
        # البحث عن منفذ متاح
        port = find_free_port()
        
        # إنشاء السيرفر
        with socketserver.TCPServer(("localhost", port), StudyPlaceHandler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"\n🚀 تم تشغيل السيرفر بنجاح!")
            print(f"📍 العنوان: {server_url}")
            print(f"🔌 المنفذ: {port}")
            print(f"📁 المجلد: {os.getcwd()}")
            
            print(f"\n📋 تعليمات الاستخدام:")
            print(f"   1. سيتم فتح المتصفح تلقائياً")
            print(f"   2. إذا لم يفتح، انسخ الرابط: {server_url}")
            print(f"   3. للإيقاف: اضغط Ctrl+C")
            
            print(f"\n✨ الميزات المتاحة:")
            print(f"   🔄 حفظ تلقائي للبيانات")
            print(f"   💾 نسخ احتياطي كل 30 دقيقة")
            print(f"   🔒 حماية من فقدان البيانات")
            print(f"   ⚡ أداء محسن")
            
            # فتح المتصفح
            open_browser_delayed(server_url)
            
            print(f"\n🟢 السيرفر يعمل... (Ctrl+C للإيقاف)")
            print("=" * 60)
            
            # تشغيل السيرفر
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 تم إيقاف السيرفر بواسطة المستخدم")
        print(f"💾 جميع البيانات محفوظة بأمان")
        print(f"📁 النسخ الاحتياطي في مجلد: backups/")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل السيرفر: {e}")
        print(f"💡 تأكد من:")
        print(f"   - عدم استخدام المنفذ من برنامج آخر")
        print(f"   - وجود صلاحيات تشغيل البرنامج")
        print(f"   - إغلاق برامج مكافحة الفيروسات مؤقتاً")
        
    finally:
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
