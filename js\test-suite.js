// Test Suite for Cafe Management System
class TestSuite {
    constructor() {
        this.tests = [];
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            progress: 0
        };
        this.startTime = null;
        this.init();
    }

    init() {
        this.log('Test Suite initialized');
        this.updateStats();
    }

    // Test execution framework
    async runTest(testName, testFunction) {
        this.log(`Running test: ${testName}`);
        const startTime = performance.now();

        try {
            const result = await testFunction();
            const endTime = performance.now();
            const duration = (endTime - startTime).toFixed(2);

            this.results.passed++;
            this.log(`✅ ${testName} - PASSED (${duration}ms)`);
            return { success: true, duration, result };
        } catch (error) {
            const endTime = performance.now();
            const duration = (endTime - startTime).toFixed(2);

            this.results.failed++;
            this.log(`❌ ${testName} - FAILED (${duration}ms): ${error.message}`);
            return { success: false, duration, error: error.message };
        } finally {
            this.results.total++;
            this.updateStats();
        }
    }

    // Update statistics display
    updateStats() {
        document.getElementById('totalTests').textContent = this.results.total;
        document.getElementById('passedTests').textContent = this.results.passed;
        document.getElementById('failedTests').textContent = this.results.failed;

        const progress = this.results.total > 0 ?
            Math.round((this.results.passed / this.results.total) * 100) : 0;

        document.getElementById('testProgress').textContent = `${progress}%`;
        document.getElementById('progressFill').style.width = `${progress}%`;
        document.getElementById('progressFill').textContent = `${progress}%`;
    }

    // Logging system
    log(message) {
        const timestamp = new Date().toLocaleTimeString('ar-EG');
        const logElement = document.getElementById('testLog');
        logElement.innerHTML += `[${timestamp}] ${message}\n`;
        logElement.scrollTop = logElement.scrollHeight;
        console.log(`[TestSuite] ${message}`);
    }

    // Display test result
    displayResult(elementId, success, message, details = '') {
        const element = document.getElementById(elementId);
        element.className = `test-result ${success ? 'success' : 'error'}`;
        element.innerHTML = `
            <strong>${success ? '✅ نجح' : '❌ فشل'}</strong><br>
            ${message}
            ${details ? `<br><small>${details}</small>` : ''}
        `;
    }
}

// Initialize test suite
const testSuite = new TestSuite();

// Storage Tests
async function testLocalStorage() {
    const result = await testSuite.runTest('LocalStorage Test', async () => {
        // Test basic localStorage functionality
        const testKey = 'test_key_' + Date.now();
        const testValue = { test: 'data', number: 123 };

        localStorage.setItem(testKey, JSON.stringify(testValue));
        const retrieved = JSON.parse(localStorage.getItem(testKey));
        localStorage.removeItem(testKey);

        if (JSON.stringify(retrieved) !== JSON.stringify(testValue)) {
            throw new Error('Data mismatch in localStorage');
        }

        return 'LocalStorage working correctly';
    });

    testSuite.displayResult('localStorageResult', result.success,
        result.success ? 'LocalStorage يعمل بشكل صحيح' : result.error);
}

async function testIndexedDB() {
    const result = await testSuite.runTest('IndexedDB Test', async () => {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('TestDB', 1);

            request.onerror = () => reject(new Error('IndexedDB not supported'));

            request.onsuccess = () => {
                const db = request.result;
                db.close();
                indexedDB.deleteDatabase('TestDB');
                resolve('IndexedDB working correctly');
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                db.createObjectStore('testStore', { keyPath: 'id' });
            };
        });
    });

    testSuite.displayResult('indexedDBResult', result.success,
        result.success ? 'IndexedDB يعمل بشكل صحيح' : result.error);
}

async function testBackup() {
    const result = await testSuite.runTest('Backup Test', async () => {
        const testData = {
            customers: [{ id: 1, name: 'Test Customer' }],
            products: [{ id: 1, name: 'Test Product' }]
        };

        const jsonString = JSON.stringify(testData);
        const blob = new Blob([jsonString], { type: 'application/json' });

        if (blob.size === 0) {
            throw new Error('Backup creation failed');
        }

        return `Backup created successfully (${blob.size} bytes)`;
    });

    testSuite.displayResult('backupResult', result.success,
        result.success ? 'النسخ الاحتياطي يعمل بشكل صحيح' : result.error);
}

// Performance Tests
async function testLoadSpeed() {
    const result = await testSuite.runTest('Load Speed Test', async () => {
        const startTime = performance.now();

        // Simulate loading operations
        await new Promise(resolve => setTimeout(resolve, 100));

        const loadTime = performance.now() - startTime;

        if (loadTime > 5000) {
            throw new Error(`Load time too slow: ${loadTime.toFixed(2)}ms`);
        }

        return `Load time: ${loadTime.toFixed(2)}ms`;
    });

    testSuite.displayResult('loadSpeedResult', result.success,
        result.success ? `سرعة التحميل جيدة: ${result.result}` : result.error);
}

async function testMemoryUsage() {
    const result = await testSuite.runTest('Memory Usage Test', async () => {
        if (!performance.memory) {
            return 'Memory API not available';
        }

        const memory = performance.memory;
        const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
        const limitMB = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2);

        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
            throw new Error(`High memory usage: ${usedMB}MB of ${limitMB}MB`);
        }

        return `Memory usage: ${usedMB}MB of ${limitMB}MB`;
    });

    testSuite.displayResult('memoryResult', result.success,
        result.success ? `استهلاك الذاكرة طبيعي: ${result.result}` : result.error);
}

async function testLoadCapacity() {
    const result = await testSuite.runTest('Load Capacity Test', async () => {
        // Test with large dataset
        const largeDataset = [];
        for (let i = 0; i < 1000; i++) {
            largeDataset.push({
                id: i,
                name: `Test Item ${i}`,
                data: 'x'.repeat(100)
            });
        }

        const startTime = performance.now();
        const jsonString = JSON.stringify(largeDataset);
        const parseTime = performance.now() - startTime;

        if (parseTime > 1000) {
            throw new Error(`Processing too slow: ${parseTime.toFixed(2)}ms`);
        }

        return `Processed 1000 items in ${parseTime.toFixed(2)}ms`;
    });

    testSuite.displayResult('loadCapacityResult', result.success,
        result.success ? `قدرة المعالجة جيدة: ${result.result}` : result.error);
}

// Functionality Tests
async function testCustomerManagement() {
    const result = await testSuite.runTest('Customer Management Test', async () => {
        // Test customer CRUD operations
        const testCustomer = {
            id: 'test_' + Date.now(),
            name: 'Test Customer',
            phone: '01234567890',
            status: 'active'
        };

        // Simulate storage operations
        localStorage.setItem(`customer_${testCustomer.id}`, JSON.stringify(testCustomer));
        const retrieved = JSON.parse(localStorage.getItem(`customer_${testCustomer.id}`));
        localStorage.removeItem(`customer_${testCustomer.id}`);

        if (retrieved.name !== testCustomer.name) {
            throw new Error('Customer data integrity failed');
        }

        return 'Customer CRUD operations working';
    });

    testSuite.displayResult('customerResult', result.success,
        result.success ? 'إدارة العملاء تعمل بشكل صحيح' : result.error);
}

async function testProductManagement() {
    const result = await testSuite.runTest('Product Management Test', async () => {
        const testProduct = {
            id: 'test_' + Date.now(),
            name: 'Test Product',
            code: 'TEST001',
            price: 10.50,
            category: 'مشروبات'
        };

        localStorage.setItem(`product_${testProduct.id}`, JSON.stringify(testProduct));
        const retrieved = JSON.parse(localStorage.getItem(`product_${testProduct.id}`));
        localStorage.removeItem(`product_${testProduct.id}`);

        if (retrieved.code !== testProduct.code) {
            throw new Error('Product data integrity failed');
        }

        return 'Product CRUD operations working';
    });

    testSuite.displayResult('productResult', result.success,
        result.success ? 'إدارة المنتجات تعمل بشكل صحيح' : result.error);
}

async function testInvoiceSystem() {
    const result = await testSuite.runTest('Invoice System Test', async () => {
        const testInvoice = {
            id: 'test_' + Date.now(),
            invoiceNumber: 'INV-TEST-001',
            customerName: 'Test Customer',
            totalCost: 25.50,
            createdAt: new Date().toISOString()
        };

        localStorage.setItem(`invoice_${testInvoice.id}`, JSON.stringify(testInvoice));
        const retrieved = JSON.parse(localStorage.getItem(`invoice_${testInvoice.id}`));
        localStorage.removeItem(`invoice_${testInvoice.id}`);

        if (retrieved.totalCost !== testInvoice.totalCost) {
            throw new Error('Invoice calculation failed');
        }

        return 'Invoice system working correctly';
    });

    testSuite.displayResult('invoiceResult', result.success,
        result.success ? 'نظام الفواتير يعمل بشكل صحيح' : result.error);
}

async function testShiftManagement() {
    const result = await testSuite.runTest('Shift Management Test', async () => {
        const testShift = {
            id: 'test_' + Date.now(),
            employeeName: 'Test Employee',
            startTime: new Date().toISOString(),
            status: 'active',
            totalSales: 0
        };

        localStorage.setItem(`shift_${testShift.id}`, JSON.stringify(testShift));
        const retrieved = JSON.parse(localStorage.getItem(`shift_${testShift.id}`));
        localStorage.removeItem(`shift_${testShift.id}`);

        if (retrieved.status !== testShift.status) {
            throw new Error('Shift status management failed');
        }

        return 'Shift management working correctly';
    });

    testSuite.displayResult('shiftResult', result.success,
        result.success ? 'إدارة الشيفتات تعمل بشكل صحيح' : result.error);
}

async function testReportSystem() {
    const result = await testSuite.runTest('Report System Test', async () => {
        // Test report generation
        const testData = {
            invoices: [
                { totalCost: 25.50, createdAt: new Date().toISOString() },
                { totalCost: 15.25, createdAt: new Date().toISOString() }
            ]
        };

        const totalRevenue = testData.invoices.reduce((sum, inv) => sum + inv.totalCost, 0);

        if (totalRevenue !== 40.75) {
            throw new Error('Report calculation failed');
        }

        return 'Report calculations working correctly';
    });

    testSuite.displayResult('reportResult', result.success,
        result.success ? 'نظام التقارير يعمل بشكل صحيح' : result.error);
}

async function testGlobalSearch() {
    const result = await testSuite.runTest('Global Search Test', async () => {
        const testData = [
            { type: 'customer', name: 'أحمد محمد', phone: '01234567890' },
            { type: 'product', name: 'قهوة', code: 'COFFEE001' }
        ];

        const searchTerm = 'أحمد';
        const results = testData.filter(item =>
            item.name && item.name.includes(searchTerm)
        );

        if (results.length === 0) {
            throw new Error('Search functionality failed');
        }

        return `Found ${results.length} results for "${searchTerm}"`;
    });

    testSuite.displayResult('searchResult', result.success,
        result.success ? 'البحث الشامل يعمل بشكل صحيح' : result.error);
}

// UI/UX Tests
async function testResponsiveDesign() {
    const result = await testSuite.runTest('Responsive Design Test', async () => {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Test CSS media queries
        const testElement = document.createElement('div');
        testElement.style.cssText = `
            width: 100%;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        `;
        document.body.appendChild(testElement);

        const computedStyle = window.getComputedStyle(testElement);
        document.body.removeChild(testElement);

        if (!computedStyle.display.includes('grid')) {
            throw new Error('CSS Grid not supported');
        }

        return `Viewport: ${viewportWidth}x${viewportHeight}, CSS Grid supported`;
    });

    testSuite.displayResult('responsiveResult', result.success,
        result.success ? 'التصميم المتجاوب يعمل بشكل صحيح' : result.error);
}

async function testDarkMode() {
    const result = await testSuite.runTest('Dark Mode Test', async () => {
        const testElement = document.createElement('div');
        testElement.setAttribute('data-theme', 'dark');
        document.body.appendChild(testElement);

        // Test CSS custom properties
        const rootStyles = getComputedStyle(document.documentElement);
        const hasCustomProperties = rootStyles.getPropertyValue('--primary-color');

        document.body.removeChild(testElement);

        if (!hasCustomProperties) {
            throw new Error('CSS custom properties not supported');
        }

        return 'Dark mode CSS variables working';
    });

    testSuite.displayResult('darkModeResult', result.success,
        result.success ? 'الوضع الليلي يعمل بشكل صحيح' : result.error);
}

async function testKeyboardShortcuts() {
    const result = await testSuite.runTest('Keyboard Shortcuts Test', async () => {
        // Test keyboard event handling
        const testEvent = new KeyboardEvent('keydown', {
            key: 'k',
            ctrlKey: true,
            bubbles: true
        });

        let eventHandled = false;
        const handler = (e) => {
            if (e.ctrlKey && e.key === 'k') {
                eventHandled = true;
            }
        };

        document.addEventListener('keydown', handler);
        document.dispatchEvent(testEvent);
        document.removeEventListener('keydown', handler);

        if (!eventHandled) {
            throw new Error('Keyboard event handling failed');
        }

        return 'Keyboard shortcuts working';
    });

    testSuite.displayResult('keyboardResult', result.success,
        result.success ? 'اختصارات لوحة المفاتيح تعمل بشكل صحيح' : result.error);
}

// Security Tests
async function testDataSecurity() {
    const result = await testSuite.runTest('Data Security Test', async () => {
        // Test data sanitization
        const maliciousInput = '<script>alert("XSS")</script>';
        const sanitized = maliciousInput.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

        if (sanitized.includes('<script>')) {
            throw new Error('XSS vulnerability detected');
        }

        // Test localStorage security
        const sensitiveData = { password: 'test123' };
        const encrypted = btoa(JSON.stringify(sensitiveData)); // Basic encoding

        if (encrypted === JSON.stringify(sensitiveData)) {
            throw new Error('Data not properly encoded');
        }

        return 'Basic security measures working';
    });

    testSuite.displayResult('securityResult', result.success,
        result.success ? 'حماية البيانات تعمل بشكل صحيح' : result.error);
}

async function testUserPermissions() {
    const result = await testSuite.runTest('User Permissions Test', async () => {
        const permissions = {
            admin: { canDelete: true, canEdit: true },
            user: { canDelete: false, canEdit: true }
        };

        const userRole = 'user';
        const hasDeletePermission = permissions[userRole]?.canDelete;

        if (hasDeletePermission) {
            throw new Error('Permission system not working correctly');
        }

        return 'Permission system working correctly';
    });

    testSuite.displayResult('permissionsResult', result.success,
        result.success ? 'صلاحيات المستخدمين تعمل بشكل صحيح' : result.error);
}

async function testDataValidation() {
    const result = await testSuite.runTest('Data Validation Test', async () => {
        // Test input validation
        const validateEmail = (email) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        };

        const validatePhone = (phone) => {
            const phoneRegex = /^01[0-9]{9}$/;
            return phoneRegex.test(phone);
        };

        if (validateEmail('invalid-email')) {
            throw new Error('Email validation failed');
        }

        if (!validatePhone('01234567890')) {
            throw new Error('Phone validation failed');
        }

        return 'Data validation working correctly';
    });

    testSuite.displayResult('validationResult', result.success,
        result.success ? 'التحقق من صحة البيانات يعمل بشكل صحيح' : result.error);
}

// Browser Compatibility Tests
async function testHTML5Features() {
    const result = await testSuite.runTest('HTML5 Features Test', async () => {
        const features = {
            localStorage: typeof Storage !== 'undefined',
            canvas: !!document.createElement('canvas').getContext,
            video: !!document.createElement('video').canPlayType,
            geolocation: !!navigator.geolocation,
            websockets: typeof WebSocket !== 'undefined'
        };

        const supportedFeatures = Object.values(features).filter(Boolean).length;
        const totalFeatures = Object.keys(features).length;

        if (supportedFeatures < totalFeatures * 0.8) {
            throw new Error(`Only ${supportedFeatures}/${totalFeatures} HTML5 features supported`);
        }

        return `${supportedFeatures}/${totalFeatures} HTML5 features supported`;
    });

    testSuite.displayResult('html5Result', result.success,
        result.success ? `ميزات HTML5 مدعومة: ${result.result}` : result.error);
}

async function testCSS3Features() {
    const result = await testSuite.runTest('CSS3 Features Test', async () => {
        const testElement = document.createElement('div');
        const features = {
            flexbox: 'flex' in testElement.style,
            grid: 'grid' in testElement.style,
            transforms: 'transform' in testElement.style,
            transitions: 'transition' in testElement.style,
            animations: 'animation' in testElement.style
        };

        const supportedFeatures = Object.values(features).filter(Boolean).length;
        const totalFeatures = Object.keys(features).length;

        if (supportedFeatures < totalFeatures * 0.8) {
            throw new Error(`Only ${supportedFeatures}/${totalFeatures} CSS3 features supported`);
        }

        return `${supportedFeatures}/${totalFeatures} CSS3 features supported`;
    });

    testSuite.displayResult('css3Result', result.success,
        result.success ? `ميزات CSS3 مدعومة: ${result.result}` : result.error);
}

async function testES6Features() {
    const result = await testSuite.runTest('ES6+ Features Test', async () => {
        try {
            // Test ES6 features
            const arrow = () => 'arrow function';
            const [a, b] = [1, 2]; // Destructuring
            const obj = { a, b }; // Object shorthand
            const template = `Template literal: ${a}`;
            const promise = new Promise(resolve => resolve('promise'));

            // Test async/await
            const asyncTest = async () => await promise;

            if (arrow() !== 'arrow function') {
                throw new Error('Arrow functions not working');
            }

            return 'ES6+ features working correctly';
        } catch (error) {
            throw new Error(`ES6+ feature failed: ${error.message}`);
        }
    });

    testSuite.displayResult('es6Result', result.success,
        result.success ? 'ميزات JavaScript ES6+ تعمل بشكل صحيح' : result.error);
}

// Utility Functions
async function runAllTests() {
    testSuite.log('Starting comprehensive test suite...');
    testSuite.startTime = performance.now();

    // Reset results
    testSuite.results = { total: 0, passed: 0, failed: 0, progress: 0 };
    testSuite.updateStats();

    // Run all tests
    const tests = [
        testLocalStorage,
        testIndexedDB,
        testBackup,
        testLoadSpeed,
        testMemoryUsage,
        testLoadCapacity,
        testCustomerManagement,
        testProductManagement,
        testInvoiceSystem,
        testShiftManagement,
        testReportSystem,
        testGlobalSearch,
        testResponsiveDesign,
        testDarkMode,
        testKeyboardShortcuts,
        testDataSecurity,
        testUserPermissions,
        testDataValidation,
        testHTML5Features,
        testCSS3Features,
        testES6Features
    ];

    for (const test of tests) {
        await test();
        await new Promise(resolve => setTimeout(resolve, 100)); // Small delay between tests
    }

    const endTime = performance.now();
    const totalTime = ((endTime - testSuite.startTime) / 1000).toFixed(2);

    testSuite.log(`All tests completed in ${totalTime} seconds`);
    testSuite.log(`Results: ${testSuite.results.passed} passed, ${testSuite.results.failed} failed`);
}

function generateTestReport() {
    const report = {
        timestamp: new Date().toISOString(),
        browser: navigator.userAgent,
        results: testSuite.results,
        duration: testSuite.startTime ?
            ((performance.now() - testSuite.startTime) / 1000).toFixed(2) + ' seconds' : 'N/A'
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test_report_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    testSuite.log('Test report generated and downloaded');
}

function clearTestData() {
    // Clear any test data from localStorage
    Object.keys(localStorage).forEach(key => {
        if (key.startsWith('test_') || key.includes('Test')) {
            localStorage.removeItem(key);
        }
    });

    testSuite.log('Test data cleared from localStorage');
    alert('تم مسح بيانات الاختبار بنجاح');
}

function openMainApp() {
    window.open('index.html', '_blank');
}

// Auto-run basic tests on page load
window.addEventListener('load', () => {
    testSuite.log('Page loaded, ready for testing');
});