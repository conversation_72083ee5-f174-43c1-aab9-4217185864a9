// Storage Management System
class StorageManager {
    constructor() {
        this.dbName = 'CafeManagementDB';
        this.version = 1;
        this.db = null;
        this.initIndexedDB();
    }

    // Initialize IndexedDB
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('IndexedDB error:', request.error);
                // Fallback to localStorage
                this.useLocalStorage = true;
                resolve();
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('IndexedDB initialized successfully');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create object stores
                if (!db.objectStoreNames.contains('customers')) {
                    const customerStore = db.createObjectStore('customers', { keyPath: 'id' });
                    customerStore.createIndex('name', 'name', { unique: false });
                    customerStore.createIndex('phone', 'phone', { unique: false });
                }

                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id' });
                    productStore.createIndex('code', 'code', { unique: true });
                    productStore.createIndex('category', 'category', { unique: false });
                }

                if (!db.objectStoreNames.contains('invoices')) {
                    const invoiceStore = db.createObjectStore('invoices', { keyPath: 'id' });
                    invoiceStore.createIndex('customerId', 'customerId', { unique: false });
                    invoiceStore.createIndex('date', 'date', { unique: false });
                }

                if (!db.objectStoreNames.contains('shifts')) {
                    const shiftStore = db.createObjectStore('shifts', { keyPath: 'id' });
                    shiftStore.createIndex('date', 'date', { unique: false });
                }

                if (!db.objectStoreNames.contains('settings')) {
                    db.createObjectStore('settings', { keyPath: 'key' });
                }

                console.log('IndexedDB stores created');
            };
        });
    }

    // Generic save method
    async save(storeName, data) {
        if (this.useLocalStorage) {
            return this.saveToLocalStorage(storeName, data);
        }
        return this.saveToIndexedDB(storeName, data);
    }

    // Generic get method
    async get(storeName, id) {
        if (this.useLocalStorage) {
            return this.getFromLocalStorage(storeName, id);
        }
        return this.getFromIndexedDB(storeName, id);
    }

    // Generic getAll method
    async getAll(storeName) {
        if (this.useLocalStorage) {
            return this.getAllFromLocalStorage(storeName);
        }
        return this.getAllFromIndexedDB(storeName);
    }

    // Generic delete method
    async delete(storeName, id) {
        if (this.useLocalStorage) {
            return this.deleteFromLocalStorage(storeName, id);
        }
        return this.deleteFromIndexedDB(storeName, id);
    }

    // IndexedDB methods
    async saveToIndexedDB(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(data);
            request.onerror = () => reject(request.error);
        });
    }

    async getFromIndexedDB(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllFromIndexedDB(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => reject(request.error);
        });
    }

    async deleteFromIndexedDB(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    // LocalStorage fallback methods
    saveToLocalStorage(storeName, data) {
        try {
            const existingData = this.getAllFromLocalStorage(storeName);
            const index = existingData.findIndex(item => item.id === data.id);
            
            if (index !== -1) {
                existingData[index] = data;
            } else {
                existingData.push(data);
            }
            
            localStorage.setItem(storeName, JSON.stringify(existingData));
            return Promise.resolve(data);
        } catch (error) {
            return Promise.reject(error);
        }
    }

    getFromLocalStorage(storeName, id) {
        try {
            const data = this.getAllFromLocalStorage(storeName);
            const item = data.find(item => item.id === id);
            return Promise.resolve(item);
        } catch (error) {
            return Promise.reject(error);
        }
    }

    getAllFromLocalStorage(storeName) {
        try {
            const data = localStorage.getItem(storeName);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return [];
        }
    }

    deleteFromLocalStorage(storeName, id) {
        try {
            const data = this.getAllFromLocalStorage(storeName);
            const filteredData = data.filter(item => item.id !== id);
            localStorage.setItem(storeName, JSON.stringify(filteredData));
            return Promise.resolve(true);
        } catch (error) {
            return Promise.reject(error);
        }
    }

    // Search methods
    async search(storeName, searchTerm, fields = []) {
        const allData = await this.getAll(storeName);
        
        if (!searchTerm) return allData;
        
        const searchLower = searchTerm.toLowerCase();
        
        return allData.filter(item => {
            if (fields.length === 0) {
                // Search in all string fields
                return Object.values(item).some(value => 
                    typeof value === 'string' && 
                    value.toLowerCase().includes(searchLower)
                );
            } else {
                // Search in specific fields
                return fields.some(field => 
                    item[field] && 
                    typeof item[field] === 'string' && 
                    item[field].toLowerCase().includes(searchLower)
                );
            }
        });
    }

    // Filter methods
    async filter(storeName, filterFn) {
        const allData = await this.getAll(storeName);
        return allData.filter(filterFn);
    }

    // Export data
    async exportData() {
        const data = {
            customers: await this.getAll('customers'),
            products: await this.getAll('products'),
            invoices: await this.getAll('invoices'),
            shifts: await this.getAll('shifts'),
            settings: await this.getAll('settings'),
            exportDate: new Date().toISOString(),
            version: this.version
        };
        
        return JSON.stringify(data, null, 2);
    }

    // Import data
    async importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            // Validate data structure
            if (!data.customers || !data.products || !data.invoices) {
                throw new Error('Invalid data format');
            }
            
            // Clear existing data
            await this.clearAll();
            
            // Import each store
            for (const customer of data.customers) {
                await this.save('customers', customer);
            }
            
            for (const product of data.products) {
                await this.save('products', product);
            }
            
            for (const invoice of data.invoices) {
                await this.save('invoices', invoice);
            }
            
            for (const shift of data.shifts) {
                await this.save('shifts', shift);
            }
            
            if (data.settings) {
                for (const setting of data.settings) {
                    await this.save('settings', setting);
                }
            }
            
            return true;
        } catch (error) {
            console.error('Import error:', error);
            throw error;
        }
    }

    // Clear all data
    async clearAll() {
        const stores = ['customers', 'products', 'invoices', 'shifts', 'settings'];
        
        for (const store of stores) {
            if (this.useLocalStorage) {
                localStorage.removeItem(store);
            } else {
                const transaction = this.db.transaction([store], 'readwrite');
                const objectStore = transaction.objectStore(store);
                await new Promise((resolve, reject) => {
                    const request = objectStore.clear();
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
            }
        }
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Get storage info
    getStorageInfo() {
        if (this.useLocalStorage) {
            let totalSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    totalSize += localStorage[key].length;
                }
            }
            return {
                type: 'localStorage',
                size: totalSize,
                sizeFormatted: this.formatBytes(totalSize)
            };
        } else {
            return {
                type: 'IndexedDB',
                size: 'Unknown',
                sizeFormatted: 'Unknown'
            };
        }
    }

    // Format bytes
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Backup data to file
    async backupToFile() {
        try {
            const data = await this.exportData();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cafe_backup_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            return true;
        } catch (error) {
            console.error('Backup error:', error);
            throw error;
        }
    }

    // Restore data from file
    async restoreFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    await this.importData(e.target.result);
                    resolve(true);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }

    // Sync data between tabs
    syncDataBetweenTabs() {
        if (this.useLocalStorage) {
            window.addEventListener('storage', (e) => {
                if (e.key && e.key.startsWith('cafe_')) {
                    // Notify other components about data changes
                    window.dispatchEvent(new CustomEvent('dataSync', {
                        detail: { key: e.key, newValue: e.newValue, oldValue: e.oldValue }
                    }));
                }
            });
        }
    }

    // Compress data before storage
    compressData(data) {
        try {
            // Simple compression by removing unnecessary whitespace
            return JSON.stringify(data);
        } catch (error) {
            console.error('Compression error:', error);
            return JSON.stringify(data);
        }
    }

    // Decompress data after retrieval
    decompressData(compressedData) {
        try {
            return JSON.parse(compressedData);
        } catch (error) {
            console.error('Decompression error:', error);
            return null;
        }
    }

    // Validate data integrity
    validateData(data, schema) {
        if (!data || typeof data !== 'object') return false;

        // Basic validation - check required fields
        const requiredFields = schema || ['id'];
        return requiredFields.every(field => data.hasOwnProperty(field));
    }

    // Get data statistics
    async getDataStatistics() {
        try {
            const stats = {
                customers: {
                    total: 0,
                    active: 0,
                    completed: 0
                },
                products: {
                    total: 0,
                    active: 0,
                    inactive: 0
                },
                invoices: {
                    total: 0,
                    today: 0,
                    thisWeek: 0,
                    thisMonth: 0
                },
                shifts: {
                    total: 0,
                    active: 0,
                    completed: 0
                },
                storage: this.getStorageInfo()
            };

            // Get customers stats
            const customers = await this.getAll('customers');
            stats.customers.total = customers.length;
            stats.customers.active = customers.filter(c => c.status === 'active').length;
            stats.customers.completed = customers.filter(c => c.status === 'completed').length;

            // Get products stats
            const products = await this.getAll('products');
            stats.products.total = products.length;
            stats.products.active = products.filter(p => p.isActive).length;
            stats.products.inactive = products.filter(p => !p.isActive).length;

            // Get invoices stats
            const invoices = await this.getAll('invoices');
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            stats.invoices.total = invoices.length;
            stats.invoices.today = invoices.filter(i => new Date(i.createdAt) >= startOfDay).length;
            stats.invoices.thisWeek = invoices.filter(i => new Date(i.createdAt) >= startOfWeek).length;
            stats.invoices.thisMonth = invoices.filter(i => new Date(i.createdAt) >= startOfMonth).length;

            // Get shifts stats
            const shifts = await this.getAll('shifts');
            stats.shifts.total = shifts.length;
            stats.shifts.active = shifts.filter(s => s.status === 'active').length;
            stats.shifts.completed = shifts.filter(s => s.status === 'completed').length;

            return stats;
        } catch (error) {
            console.error('Error getting data statistics:', error);
            return null;
        }
    }

    // Clean old data
    async cleanOldData(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            // Clean old completed customers
            const customers = await this.getAll('customers');
            const oldCustomers = customers.filter(c =>
                c.status === 'completed' && new Date(c.updatedAt) < cutoffDate
            );

            for (const customer of oldCustomers) {
                await this.delete('customers', customer.id);
            }

            // Clean old invoices (keep for longer period)
            const invoices = await this.getAll('invoices');
            const oldInvoices = invoices.filter(i =>
                new Date(i.createdAt) < new Date(cutoffDate.getTime() - (365 * 24 * 60 * 60 * 1000)) // Keep for 1 year
            );

            for (const invoice of oldInvoices) {
                await this.delete('invoices', invoice.id);
            }

            return {
                customersDeleted: oldCustomers.length,
                invoicesDeleted: oldInvoices.length
            };
        } catch (error) {
            console.error('Error cleaning old data:', error);
            throw error;
        }
    }

    // Monitor storage usage
    monitorStorageUsage() {
        if (this.useLocalStorage) {
            const checkUsage = () => {
                const info = this.getStorageInfo();
                const usagePercent = (info.size / (5 * 1024 * 1024)) * 100; // Assume 5MB limit

                if (usagePercent > 80) {
                    console.warn('Storage usage is high:', usagePercent.toFixed(2) + '%');
                    // Trigger cleanup or notify user
                    window.dispatchEvent(new CustomEvent('storageWarning', {
                        detail: { usage: usagePercent, size: info.size }
                    }));
                }
            };

            // Check every 5 minutes
            setInterval(checkUsage, 5 * 60 * 1000);
            checkUsage(); // Initial check
        }
    }

    // Create data migration utilities
    async migrateData(fromVersion, toVersion) {
        try {
            console.log(`Migrating data from version ${fromVersion} to ${toVersion}`);

            // Add migration logic here based on version differences
            if (fromVersion < 1.1 && toVersion >= 1.1) {
                // Example migration: add new fields to existing records
                await this.migrateToV1_1();
            }

            // Update version in settings
            await this.save('settings', { key: 'dataVersion', value: toVersion });

            return true;
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }

    // Example migration function
    async migrateToV1_1() {
        // Add any new fields or restructure data as needed
        const customers = await this.getAll('customers');
        for (const customer of customers) {
            if (!customer.hasOwnProperty('version')) {
                customer.version = '1.1';
                customer.lastActivity = customer.updatedAt;
                await this.save('customers', customer);
            }
        }
    }
}

// Initialize storage manager
const storage = new StorageManager();

// Setup data sync and monitoring
storage.syncDataBetweenTabs();
storage.monitorStorageUsage();

// Export for use in other modules
window.storage = storage;
