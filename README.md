# نظام إدارة مكان المذاكرة - Study Place Management System

نظام إدارة شامل لأماكن المذاكرة والدراسة يعمل محلياً في المتصفح بدون الحاجة إلى اتصال إنترنت أو سيرفر خارجي.

## المميزات الرئيسية

### 👨‍🎓 إدارة الطلاب
- إضافة طلاب جدد مع البيانات الأساسية (الاسم، الهاتف، ملاحظات)
- **تايمر مرئي في الوقت الفعلي** لكل طالب يذاكر
- حساب الوقت المستغرق تلقائياً من وقت الدخول
- تتبع الطلاب النشطين (يذاكرون حالياً) والمكتملين
- **إنشاء فاتورة في أي وقت** بدون إنهاء الجلسة
- تعديل وقت الجلسة يدوياً
- عرض تفاصيل شاملة لكل طالب

### 🛒 إدارة المنتجات والمبيعات
- نظام تكويد للمشروبات والمنتجات مع الأسعار
- تصنيف المنتجات (مشروبات، وجبات خفيفة، حلويات، قرطاسية، أخرى)
- إضافة وتعديل وحذف المنتجات
- البحث في المنتجات بالاسم أو الكود
- إضافة سريعة للمنتجات للطلاب النشطين

### 🧾 نظام الفواتير
- توليد فواتير تلقائية تشمل الوقت والمشتريات
- طباعة الفواتير من المتصفح مباشرة بتنسيق منظم
- عرض تفاصيل شاملة لكل فاتورة
- البحث في الفواتير
- تصفية الفواتير حسب التاريخ

### ⏰ إدارة الشيفتات
- **تكويد تلقائي للشيفتات** (مثال: SH-250628-001)
- فتح وإغلاق الشيفتات مع حفظ دائم
- **حفظ تلقائي كل 30 ثانية** لضمان عدم فقدان البيانات
- تسجيل مبيعات كل شيفت
- إضافة وإدارة المصاريف اليومية
- حساب صافي الأرباح
- **استعادة البيانات** بعد تحديث الصفحة
- عرض تاريخ الشيفتات السابقة مع الأكواد

### 📊 التقارير والإحصائيات
- تقارير يومية، أسبوعية، وشهرية
- إحصائيات المبيعات والأرباح
- تحليل أداء المنتجات
- إحصائيات العملاء ومتوسط الإنفاق
- تحليل أوقات الذروة

### 💾 التخزين المحلي المتقدم
- حفظ جميع البيانات في LocalStorage/IndexedDB
- **حفظ تلقائي كل 30 ثانية** للشيفت النشط
- **نسخ احتياطي مضاعف** في localStorage و IndexedDB
- **استعادة تلقائية** للبيانات بعد تحديث الصفحة
- لا يتم فقدان البيانات بعد إغلاق المتصفح
- نسخ احتياطي واستعادة البيانات يدوياً
- تصدير واستيراد البيانات بصيغة JSON
- **حفظ عند تغيير التبويب** أو إغلاق النافذة

## التشغيل السريع

### 🚀 للبدء الفوري:
1. **افتح `demo.html`** - عرض توضيحي تفاعلي للنظام
2. **افتح `index.html`** - النظام الرئيسي للاستخدام
3. **افتح `test.html`** - اختبار سريع للوظائف

### ⏱️ الميزات الجديدة المهمة:
- **تايمر مرئي بالثواني** لكل طالب يذاكر
- **إنشاء فاتورة فورية** بدون إنهاء الجلسة
- **تعديل الوقت يدوياً** للطلاب النشطين
- **فواتير جزئية** مع إمكانية المتابعة
- **تكويد تلقائي للشيفتات** مع حفظ دائم
- **حفظ تلقائي كل 30 ثانية** لضمان عدم فقدان البيانات
- **استعادة البيانات** التلقائية بعد تحديث الصفحة

**لا يتطلب النظام:**
- اتصال إنترنت
- تثبيت برامج إضافية
- إعداد سيرفر
- قواعد بيانات خارجية

## هيكل المشروع

```
cafe-management/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── styles.css          # الأنماط الرئيسية
│   └── print.css           # أنماط الطباعة
├── js/
│   ├── app.js              # التطبيق الرئيسي
│   ├── storage.js          # إدارة التخزين
│   ├── customers.js        # إدارة العملاء
│   ├── products.js         # إدارة المنتجات
│   ├── invoices.js         # إدارة الفواتير
│   ├── shifts.js           # إدارة الشيفتات
│   └── reports.js          # التقارير
└── README.md               # هذا الملف
```

## الواجهة والتصميم

### 🌙 الوضع الليلي
- دعم كامل للوضع الليلي والنهاري
- تبديل سهل بين الأوضاع
- حفظ تفضيل المستخدم

### 📱 التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- تصميم متجاوب للهواتف والأجهزة اللوحية
- واجهة سهلة الاستخدام

### ⌨️ اختصارات لوحة المفاتيح
- `Ctrl+1`: لوحة التحكم
- `Ctrl+2`: العملاء
- `Ctrl+3`: المنتجات
- `Ctrl+4`: الفواتير
- `Ctrl+N`: إضافة جديد (حسب القسم الحالي)
- `Escape`: إغلاق النوافذ المنبثقة

## الاستخدام

### إضافة طالب جديد
1. اذهب إلى قسم "العملاء"
2. اضغط على "إضافة عميل"
3. أدخل البيانات المطلوبة (الاسم، الهاتف)
4. سيبدأ التايمر تلقائياً ويظهر الوقت بالثواني

### إنشاء فاتورة فورية (بدون إنهاء الجلسة)
1. من بطاقة الطالب النشط، اضغط زر "إنشاء فاتورة" 🧾
2. ستُنشأ فاتورة جزئية بالوقت والتكلفة الحالية
3. يمكن طباعة الفاتورة فوراً
4. **الطالب يستمر في المذاكرة** والتايمر يعمل

### إضافة مشتريات للطالب
1. من بطاقة الطالب النشط، اضغط "إضافة مشتريات"
2. اختر المنتجات المطلوبة (مشروبات، قرطاسية، وجبات)
3. ستُضاف تلقائياً إلى حساب الطالب والتكلفة تتحدث فوراً

### تعديل وقت الجلسة
1. اضغط زر "تعديل الوقت" ⏰ في بطاقة الطالب
2. أدخل الوقت الجديد بالدقائق
3. سيتم تحديث التايمر والتكلفة

### إنهاء جلسة الطالب
1. اضغط على زر "إنهاء الجلسة" ⏹️ في بطاقة الطالب
2. ستُحسب التكلفة النهائية تلقائياً
3. ستُنشأ فاتورة نهائية قابلة للطباعة

### فتح شيفت جديد
1. اذهب إلى قسم "الشيفتات"
2. اضغط "فتح شيفت جديد"
3. أدخل اسم الموظف ورصيد البداية
4. **سيتم إنشاء كود تلقائي** للشيفت (مثال: SH-250628-001)
5. **الحفظ التلقائي** يبدأ فوراً كل 30 ثانية

### إدارة الشيفت النشط
- **البيانات محفوظة تلقائياً** - لا تقلق من تحديث الصفحة
- إضافة المصاريف مع حفظ فوري
- مراقبة المبيعات والإحصائيات في الوقت الفعلي
- **استعادة تلقائية** للشيفت بعد إعادة فتح الصفحة

### إغلاق الشيفت
1. اضغط "إغلاق الشيفت" من قسم الشيفتات
2. أدخل رصيد النهاية
3. **سيتم حفظ جميع البيانات نهائياً** مع الكود
4. إنشاء تقرير نهائي قابل للطباعة

## المتطلبات التقنية

- متصفح حديث يدعم:
  - HTML5
  - CSS3
  - JavaScript ES6+
  - LocalStorage
  - IndexedDB

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## النسخ الاحتياطي

### تصدير البيانات
1. اذهب إلى "الإعدادات"
2. اضغط "تصدير البيانات"
3. سيتم تحميل ملف JSON يحتوي على جميع البيانات

### استيراد البيانات
1. اذهب إلى "الإعدادات"
2. اضغط "استيراد البيانات"
3. اختر ملف النسخة الاحتياطية

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً في جهازك
- لا يتم إرسال أي بيانات لخوادم خارجية
- البيانات آمنة ومحمية بواسطة المتصفح

## الدعم والمساعدة

### المشاكل الشائعة

**البيانات لا تُحفظ:**
- تأكد من أن المتصفح يدعم LocalStorage
- تحقق من عدم استخدام وضع التصفح الخاص

**الطباعة لا تعمل:**
- تأكد من تفعيل الطباعة في المتصفح
- جرب متصفح آخر

**الواجهة لا تظهر بشكل صحيح:**
- تأكد من تحميل جميع ملفات CSS
- امسح ذاكرة التخزين المؤقت للمتصفح

## التطوير والتخصيص

النظام مبني بتقنيات ويب قياسية ويمكن تخصيصه بسهولة:

- **HTML**: هيكل الصفحات
- **CSS**: التصميم والأنماط
- **JavaScript**: المنطق والوظائف

### إضافة ميزات جديدة
1. أضف الواجهة في `index.html`
2. أضف الأنماط في `css/styles.css`
3. أضف المنطق في ملف JavaScript منفصل

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

## الإصدار

الإصدار الحالي: 1.0.0

---

## ملفات المشروع

### الملفات الأساسية
- `index.html` - الصفحة الرئيسية للتطبيق
- `test.html` - صفحة اختبار سريع للوظائف الأساسية
- `test-suite.html` - مجموعة اختبارات شاملة للنظام

### ملفات CSS
- `css/styles.css` - الأنماط الرئيسية للتطبيق
- `css/print.css` - أنماط خاصة بالطباعة

### ملفات JavaScript
- `js/app.js` - التطبيق الرئيسي ونقطة البداية
- `js/storage.js` - نظام إدارة التخزين المحلي
- `js/customers.js` - إدارة العملاء والجلسات
- `js/products.js` - إدارة المنتجات والمشروبات
- `js/invoices.js` - نظام الفواتير والطباعة
- `js/shifts.js` - إدارة الشيفتات والمصاريف
- `js/reports.js` - نظام التقارير والإحصائيات
- `js/advanced-features.js` - الميزات الاحترافية والبحث
- `js/test-suite.js` - مجموعة الاختبارات الشاملة

## التشغيل السريع

1. **تحميل المشروع**: قم بتحميل جميع الملفات في مجلد واحد
2. **فتح التطبيق**: افتح `index.html` في المتصفح
3. **اختبار النظام**: افتح `test.html` لاختبار سريع أو `test-suite.html` لاختبار شامل
4. **البدء**: ابدأ بإضافة المنتجات ثم العملاء

## الميزات المتقدمة

### 🔍 البحث الشامل
- اضغط `Ctrl+K` للبحث في جميع البيانات
- البحث في العملاء، المنتجات، والفواتير
- نتائج فورية مع التنقل المباشر

### 👥 نظام الصلاحيات
- ثلاثة مستويات: مدير، كاشير، مراقب
- تحكم في الوصول للوظائف حسب الدور
- حماية البيانات الحساسة

### 📊 تقارير متقدمة
- تقارير مخصصة لأي فترة زمنية
- تحليل أداء المنتجات
- إحصائيات العملاء والأوقات
- تصدير التقارير بصيغ متعددة

### 💾 إدارة البيانات
- نسخ احتياطي تلقائي
- استيراد وتصدير البيانات
- تنظيف البيانات القديمة
- مراقبة استهلاك التخزين

## الاختبار والجودة

### اختبار سريع
افتح `test.html` لاختبار الوظائف الأساسية:
- التخزين المحلي
- إضافة بيانات تجريبية
- اختبار الطباعة

### اختبار شامل
افتح `test-suite.html` لاختبار متقدم يشمل:
- اختبارات الأداء
- اختبارات الأمان
- اختبارات التوافق
- اختبارات واجهة المستخدم

## الدعم الفني

### متطلبات النظام
- متصفح حديث (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- JavaScript مفعل
- 10 ميجابايت مساحة تخزين محلية

### استكشاف الأخطاء
1. **البيانات لا تُحفظ**: تأكد من عدم استخدام الوضع الخاص
2. **الطباعة لا تعمل**: تحقق من إعدادات المتصفح
3. **بطء في الأداء**: استخدم أداة تنظيف البيانات القديمة

### الحصول على المساعدة
- راجع ملف `test-suite.html` لتشخيص المشاكل
- تحقق من وحدة التحكم في المتصفح للأخطاء
- استخدم ميزة النسخ الاحتياطي قبل أي تغييرات مهمة

## التطوير والتخصيص

### إضافة ميزات جديدة
1. أضف الواجهة في `index.html`
2. أضف الأنماط في `css/styles.css`
3. أضف المنطق في ملف JavaScript منفصل
4. اربط الملف الجديد في `index.html`

### تخصيص التصميم
- عدّل متغيرات CSS في `:root`
- استخدم الفئات الجاهزة للتصميم
- اتبع نمط التصميم الموجود

### إضافة اختبارات
- أضف اختبارات جديدة في `js/test-suite.js`
- اتبع نمط الاختبارات الموجود
- شغّل الاختبارات للتأكد من عدم كسر الوظائف

## الأمان والخصوصية

- ✅ جميع البيانات محفوظة محلياً
- ✅ لا يتم إرسال بيانات لخوادم خارجية
- ✅ تشفير أساسي للبيانات الحساسة
- ✅ حماية من هجمات XSS
- ✅ نظام صلاحيات متدرج

## الإصدارات والتحديثات

### الإصدار الحالي: 1.0.0
- ✅ جميع الوظائف الأساسية
- ✅ واجهة مستخدم كاملة
- ✅ نظام اختبارات شامل
- ✅ توثيق كامل

### التحديثات المستقبلية
- إضافة المزيد من التقارير
- تحسين الأداء
- ميزات إضافية حسب الطلب

---

**تم تطوير هذا النظام ليكون حلاً شاملاً وسهل الاستخدام لإدارة المقاهي والكافيهات بدون تعقيدات تقنية.**

**🎯 جاهز للاستخدام الفوري - لا يتطلب تثبيت أو إعداد معقد!**
