// Product Management System
class ProductManager {
    constructor() {
        this.products = [];
        this.categories = ['مشروبات', 'وجبات خفيفة', 'حلويات', 'قرطاسية', 'أخرى'];
        this.init();
    }

    async init() {
        await this.loadProducts();
        this.renderProducts();
        this.setupEventListeners();
        await this.initializeDefaultProducts();
    }

    // Load products from storage
    async loadProducts() {
        try {
            this.products = await storage.getAll('products');
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = [];
        }
    }

    // Initialize default products if none exist
    async initializeDefaultProducts() {
        if (this.products.length === 0) {
            const defaultProducts = [
                { name: 'شاي', code: 'TEA001', price: 5, category: 'مشروبات' },
                { name: 'قهوة', code: 'COFFEE001', price: 8, category: 'مشروبات' },
                { name: 'نسكافيه', code: 'NESC001', price: 10, category: 'مشروبات' },
                { name: 'عصير برتقال', code: 'JUICE001', price: 12, category: 'مشروبات' },
                { name: 'مياه', code: 'WATER001', price: 3, category: 'مشروبات' },
                { name: 'قلم رصاص', code: 'PEN001', price: 2, category: 'قرطاسية' },
                { name: 'دفتر', code: 'NOTE001', price: 10, category: 'قرطاسية' },
                { name: 'سندوتش جبنة', code: 'SAND001', price: 15, category: 'وجبات خفيفة' },
                { name: 'كيك', code: 'CAKE001', price: 20, category: 'حلويات' },
                { name: 'بسكويت', code: 'BISCUIT001', price: 8, category: 'وجبات خفيفة' }
            ];

            for (const productData of defaultProducts) {
                await this.addProduct(productData, false); // false = don't render yet
            }
            this.renderProducts();
        }
    }

    // Add new product
    async addProduct(productData, shouldRender = true) {
        try {
            // Validate product code uniqueness
            const existingProduct = this.products.find(p => p.code === productData.code);
            if (existingProduct) {
                throw new Error('كود المنتج موجود بالفعل');
            }

            const product = {
                id: storage.generateId(),
                name: productData.name.trim(),
                code: productData.code.trim().toUpperCase(),
                price: parseFloat(productData.price),
                category: productData.category,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await storage.save('products', product);
            this.products.push(product);
            
            if (shouldRender) {
                this.renderProducts();
                this.addActivity(`تم إضافة المنتج: ${product.name}`);
            }
            
            return product;
        } catch (error) {
            console.error('Error adding product:', error);
            throw error;
        }
    }

    // Update product
    async updateProduct(productId, updates) {
        try {
            const productIndex = this.products.findIndex(p => p.id === productId);
            if (productIndex === -1) {
                throw new Error('Product not found');
            }

            // Check code uniqueness if code is being updated
            if (updates.code) {
                const existingProduct = this.products.find(p => p.code === updates.code && p.id !== productId);
                if (existingProduct) {
                    throw new Error('كود المنتج موجود بالفعل');
                }
            }

            const product = { 
                ...this.products[productIndex], 
                ...updates,
                updatedAt: new Date().toISOString()
            };

            await storage.save('products', product);
            this.products[productIndex] = product;
            
            this.renderProducts();
            this.addActivity(`تم تحديث المنتج: ${product.name}`);
            
            return product;
        } catch (error) {
            console.error('Error updating product:', error);
            throw error;
        }
    }

    // Delete product
    async deleteProduct(productId) {
        try {
            await storage.delete('products', productId);
            this.products = this.products.filter(p => p.id !== productId);
            
            this.renderProducts();
            this.addActivity(`تم حذف منتج`);
        } catch (error) {
            console.error('Error deleting product:', error);
            throw error;
        }
    }

    // Toggle product active status
    async toggleProductStatus(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        await this.updateProduct(productId, {
            isActive: !product.isActive
        });
    }

    // Search products
    async searchProducts(searchTerm) {
        if (!searchTerm) {
            this.renderProducts();
            return;
        }

        const results = await storage.search('products', searchTerm, ['name', 'code', 'category']);
        this.renderProducts(results);
    }

    // Filter products by category
    filterByCategory(category) {
        if (!category || category === 'all') {
            this.renderProducts();
            return;
        }

        const filtered = this.products.filter(p => p.category === category);
        this.renderProducts(filtered);
    }

    // Get product by code
    getProductByCode(code) {
        return this.products.find(p => p.code === code.toUpperCase());
    }

    // Get active products
    getActiveProducts() {
        return this.products.filter(p => p.isActive);
    }

    // Get products by category
    getProductsByCategory(category) {
        return this.products.filter(p => p.category === category && p.isActive);
    }

    // Render products
    renderProducts(productsToRender = null) {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        const products = productsToRender || this.products;
        
        if (products.length === 0) {
            productsGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-coffee fa-3x"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>ابدأ بإضافة منتج جديد</p>
                </div>
            `;
            return;
        }

        // Group products by category
        const groupedProducts = this.groupProductsByCategory(products);
        
        productsGrid.innerHTML = Object.keys(groupedProducts).map(category => 
            this.createCategorySection(category, groupedProducts[category])
        ).join('');
    }

    // Group products by category
    groupProductsByCategory(products) {
        return products.reduce((groups, product) => {
            const category = product.category || 'أخرى';
            if (!groups[category]) {
                groups[category] = [];
            }
            groups[category].push(product);
            return groups;
        }, {});
    }

    // Create category section HTML
    createCategorySection(category, products) {
        return `
            <div class="category-section">
                <h3 class="category-title">
                    <i class="fas fa-${this.getCategoryIcon(category)}"></i>
                    ${category} (${products.length})
                </h3>
                <div class="products-category-grid">
                    ${products.map(product => this.createProductCard(product)).join('')}
                </div>
            </div>
        `;
    }

    // Get category icon
    getCategoryIcon(category) {
        const icons = {
            'مشروبات': 'coffee',
            'مأكولات': 'utensils',
            'حلويات': 'birthday-cake',
            'أخرى': 'box'
        };
        return icons[category] || 'box';
    }

    // Create product card HTML
    createProductCard(product) {
        const statusClass = product.isActive ? 'status-active' : 'status-inactive';
        const statusText = product.isActive ? 'متاح' : 'غير متاح';

        return `
            <div class="product-card fade-in ${!product.isActive ? 'product-inactive' : ''}">
                <div class="card-header">
                    <div class="card-title">${product.name}</div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-${product.isActive ? 'warning' : 'success'}" 
                                onclick="productManager.toggleProductStatus('${product.id}')" 
                                title="${product.isActive ? 'إيقاف' : 'تفعيل'}">
                            <i class="fas fa-${product.isActive ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-sm btn-info" 
                                onclick="productManager.editProduct('${product.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" 
                                onclick="productManager.confirmDelete('${product.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="card-info">
                    <p><strong>الكود:</strong> ${product.code}</p>
                    <p><strong>السعر:</strong> ${product.price.toFixed(2)} جنيه</p>
                    <p><strong>الفئة:</strong> ${product.category}</p>
                    <p><strong>الحالة:</strong> <span class="status-badge ${statusClass}">${statusText}</span></p>
                </div>
                
                <div class="product-actions">
                    <button class="btn btn-sm btn-primary" 
                            onclick="productManager.quickAddToCustomer('${product.id}')"
                            ${!product.isActive ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i> إضافة سريعة
                    </button>
                </div>
            </div>
        `;
    }

    // Setup event listeners
    setupEventListeners() {
        // Product search
        const searchInput = document.getElementById('productSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchProducts(e.target.value);
            });
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }

        // Barcode scanner simulation (Enter key on product code input)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.target.id === 'productCode') {
                e.preventDefault();
                this.handleBarcodeInput(e.target.value);
            }
        });
    }

    // Handle barcode input
    handleBarcodeInput(code) {
        const product = this.getProductByCode(code);
        if (product) {
            // Product found, add to active customer if any
            this.quickAddToCustomer(product.id);
        } else {
            // Product not found, suggest adding new product
            if (confirm(`المنتج بالكود "${code}" غير موجود. هل تريد إضافته؟`)) {
                document.getElementById('productCode').value = code;
                this.showAddProductModal();
            }
        }
    }

    // Edit product
    editProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // Fill form with product data
        document.getElementById('productName').value = product.name;
        document.getElementById('productCode').value = product.code;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productCategory').value = product.category;

        // Store product ID for update
        document.getElementById('productForm').dataset.productId = productId;
        
        // Show modal
        showModal('addProductModal');
    }

    // Confirm delete product
    confirmDelete(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        if (confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
            this.deleteProduct(productId);
        }
    }

    // Quick add product to customer
    quickAddToCustomer(productId) {
        const activeCustomers = Array.from(customerManager.activeCustomers.values());
        
        if (activeCustomers.length === 0) {
            alert('لا توجد عملاء نشطين حالياً');
            return;
        }

        if (activeCustomers.length === 1) {
            // Add to the only active customer
            customerManager.addPurchase(activeCustomers[0].id, productId);
        } else {
            // Show customer selection modal
            this.showCustomerSelectionModal(productId);
        }
    }

    // Show customer selection modal
    showCustomerSelectionModal(productId) {
        const activeCustomers = Array.from(customerManager.activeCustomers.values());
        const product = this.products.find(p => p.id === productId);
        
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>اختر العميل - ${product.name}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="customer-selection">
                        ${activeCustomers.map(customer => `
                            <div class="customer-option" onclick="productManager.addToSelectedCustomer('${customer.id}', '${productId}')">
                                <h4>${customer.name}</h4>
                                <p>الوقت: ${customerManager.calculateCurrentTime(customer.entryTime)} دقيقة</p>
                                <p>التكلفة الحالية: ${customer.totalCost.toFixed(2)} جنيه</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Add product to selected customer
    async addToSelectedCustomer(customerId, productId) {
        await customerManager.addPurchase(customerId, productId);
        closeModal();
    }

    // Add activity to recent activity list
    addActivity(message) {
        if (window.activityManager) {
            window.activityManager.addActivity(message);
        }
    }

    // Get product statistics
    getProductStats() {
        const stats = {
            total: this.products.length,
            active: this.products.filter(p => p.isActive).length,
            inactive: this.products.filter(p => !p.isActive).length,
            byCategory: {}
        };

        this.categories.forEach(category => {
            stats.byCategory[category] = this.products.filter(p => p.category === category).length;
        });

        return stats;
    }
}

// Initialize product manager
const productManager = new ProductManager();
