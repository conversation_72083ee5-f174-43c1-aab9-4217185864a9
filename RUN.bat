@echo off
chcp 65001 >nul
title نظام إدارة مكان المذاكرة - تشغيل سريع

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎓 نظام إدارة مكان المذاكرة                    ║
echo ║                      Study Place Management                  ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  ⚡ تشغيل سريع مع اختبار تلقائي                               ║
echo ║  🚀 حل شامل لإدارة أماكن المذاكرة                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 مرحباً بك في نظام إدارة مكان المذاكرة
echo.
echo 📋 الخيارات المتاحة:
echo    1. تشغيل النظام مباشرة
echo    2. اختبار النظام أولاً ثم التشغيل
echo    3. عرض الدليل السريع
echo    4. خروج
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" goto :run_direct
if "%choice%"=="2" goto :test_then_run
if "%choice%"=="3" goto :show_guide
if "%choice%"=="4" goto :exit

echo ❌ خيار غير صحيح
goto :end

:run_direct
echo.
echo 🚀 تشغيل النظام مباشرة...
echo.
call start_server.bat
goto :end

:test_then_run
echo.
echo 🧪 اختبار النظام أولاً...
echo.

REM فحص Python سريع
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    goto :end
)

echo ✅ Python مثبت

REM فحص الملفات الأساسية
if not exist "index.html" (
    echo ❌ ملف index.html غير موجود
    goto :missing_files
)

if not exist "server.py" (
    echo ❌ ملف server.py غير موجود
    goto :missing_files
)

echo ✅ الملفات الأساسية موجودة

echo.
echo ✅ الاختبار السريع نجح!
echo 🚀 تشغيل النظام...
echo.

call start_server.bat
goto :end

:show_guide
echo.
echo 📖 الدليل السريع:
echo.
echo 🎯 للبدء:
echo    1. تأكد من تثبيت Python
echo    2. شغل هذا الملف واختر الخيار 1 أو 2
echo    3. سيفتح المتصفح تلقائياً
echo.
echo 🔥 الميزات الرئيسية:
echo    ⏱️  تايمر مرئي للطلاب
echo    🧾 فواتير ذكية
echo    💾 حفظ آمن للبيانات
echo    📊 تقارير شاملة
echo.
echo 📞 للمساعدة:
echo    - اقرأ ملف README.md
echo    - اقرأ ملف QUICK_START.md
echo.
pause
goto :end

:missing_files
echo.
echo ❌ ملفات مفقودة! تأكد من تحميل جميع ملفات النظام
echo.
echo 📁 الملفات المطلوبة:
echo    - index.html
echo    - server.py
echo    - start_server.bat
echo    - مجلد js/ مع جميع ملفات JavaScript
echo    - مجلد css/ مع ملفات الأنماط
echo.
pause
goto :end

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة مكان المذاكرة
goto :end

:end
echo.
pause
