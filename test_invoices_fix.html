<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الفواتير - سكون</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.danger {
            background: #e74c3c;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .invoice-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .invoice-details {
            flex: 1;
        }
        .invoice-amount {
            font-weight: bold;
            color: #27ae60;
            font-size: 1.2em;
        }
        .service-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        .service-hourly {
            background: #3498db;
            color: white;
        }
        .service-daily {
            background: #f39c12;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار إصلاح الفواتير</h1>
            <p>اختبار شامل لنظام الفواتير مع خيار إيجار 24 ساعة</p>
        </div>

        <div class="test-section">
            <h3>📋 معلومات التحديثات</h3>
            <div class="info">
                <strong>التحديثات المطبقة:</strong><br>
                ✅ إصلاح مشكلة عدم ظهور الفواتير<br>
                ✅ إضافة خيار إيجار 24 ساعة<br>
                ✅ تغيير اسم المكان إلى "سكون"<br>
                ✅ نظام احتياطي للفواتير في localStorage<br>
                ✅ تحديث حساب التكلفة للخدمات المختلفة
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 إدارة الفواتير</h3>
            <button class="test-button" onclick="createTestInvoices()">إنشاء فواتير تجريبية</button>
            <button class="test-button" onclick="listInvoices()">عرض قائمة الفواتير</button>
            <button class="test-button danger" onclick="clearAllInvoices()">حذف جميع الفواتير</button>
            <div id="invoiceResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💰 اختبار خيارات التسعير</h3>
            <button class="test-button" onclick="testHourlyService()">اختبار الخدمة بالساعة</button>
            <button class="test-button" onclick="testDailyService()">اختبار إيجار 24 ساعة</button>
            <button class="test-button" onclick="updatePriceSettings()">تحديث إعدادات الأسعار</button>
            <div id="pricingResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🚀 تشغيل النظام الكامل</h3>
            <p>بعد اكتمال الاختبارات بنجاح:</p>
            <button class="test-button success" onclick="openMainSystem()" style="font-size: 16px; padding: 15px 30px;">
                🎯 فتح نظام إدارة سكون
            </button>
        </div>
    </div>

    <script>
        // إنشاء فواتير تجريبية
        function createTestInvoices() {
            const result = document.getElementById('invoiceResult');
            result.style.display = 'block';
            
            try {
                const testInvoices = [
                    {
                        id: 'inv_001',
                        invoiceNumber: 'INV-24-001',
                        customerName: 'أحمد محمد',
                        customerPhone: '01234567890',
                        serviceType: 'hourly',
                        entryTime: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
                        exitTime: new Date().toISOString(),
                        totalTime: 120, // 2 hours
                        timeCost: 20, // 2 * 10
                        purchasesCost: 15,
                        totalCost: 35,
                        finalAmount: 35,
                        status: 'completed',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'inv_002',
                        invoiceNumber: 'INV-24-002',
                        customerName: 'فاطمة علي',
                        customerPhone: '01987654321',
                        serviceType: 'daily',
                        entryTime: new Date(Date.now() - 86400000).toISOString(), // 24 hours ago
                        exitTime: new Date().toISOString(),
                        totalTime: 1440, // 24 hours
                        timeCost: 65, // daily rate
                        purchasesCost: 25,
                        totalCost: 90,
                        finalAmount: 90,
                        status: 'completed',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'inv_003',
                        invoiceNumber: 'INV-24-003',
                        customerName: 'محمد حسن',
                        customerPhone: '01555666777',
                        serviceType: 'hourly',
                        entryTime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                        exitTime: new Date().toISOString(),
                        totalTime: 60, // 1 hour
                        timeCost: 10, // 1 * 10
                        purchasesCost: 0,
                        totalCost: 10,
                        finalAmount: 10,
                        status: 'completed',
                        createdAt: new Date().toISOString()
                    }
                ];

                // حفظ في localStorage
                testInvoices.forEach(invoice => {
                    localStorage.setItem(`invoice_${invoice.id}`, JSON.stringify(invoice));
                });

                result.className = 'result success';
                result.innerHTML = `
                    ✅ تم إنشاء ${testInvoices.length} فاتورة تجريبية بنجاح!<br>
                    <div style="margin-top: 10px;">
                        ${testInvoices.map(inv => `
                            <div class="invoice-item">
                                <div class="invoice-details">
                                    <strong>${inv.customerName}</strong> - ${inv.invoiceNumber}
                                    <span class="service-badge ${inv.serviceType === 'daily' ? 'service-daily' : 'service-hourly'}">
                                        ${inv.serviceType === 'daily' ? 'إيجار 24 ساعة' : 'بالساعة'}
                                    </span>
                                </div>
                                <div class="invoice-amount">${inv.finalAmount} جنيه</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في إنشاء الفواتير: ${error.message}`;
            }
        }

        // عرض قائمة الفواتير
        function listInvoices() {
            const result = document.getElementById('invoiceResult');
            result.style.display = 'block';
            
            try {
                const invoices = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('invoice_')) {
                        const inv = JSON.parse(localStorage.getItem(key));
                        invoices.push(inv);
                    }
                }

                if (invoices.length === 0) {
                    result.className = 'result info';
                    result.innerHTML = 'ℹ️ لا توجد فواتير. اضغط "إنشاء فواتير تجريبية" أولاً.';
                } else {
                    const totalAmount = invoices.reduce((sum, inv) => sum + inv.finalAmount, 0);
                    const hourlyCount = invoices.filter(inv => inv.serviceType === 'hourly').length;
                    const dailyCount = invoices.filter(inv => inv.serviceType === 'daily').length;

                    result.className = 'result success';
                    result.innerHTML = `
                        ✅ تم العثور على ${invoices.length} فاتورة:<br>
                        📊 <strong>الإحصائيات:</strong><br>
                        - إجمالي المبلغ: ${totalAmount.toFixed(2)} جنيه<br>
                        - فواتير بالساعة: ${hourlyCount}<br>
                        - فواتير إيجار 24 ساعة: ${dailyCount}<br><br>
                        <div>
                            ${invoices.map(inv => `
                                <div class="invoice-item">
                                    <div class="invoice-details">
                                        <strong>${inv.customerName}</strong> - ${inv.invoiceNumber}
                                        <span class="service-badge ${inv.serviceType === 'daily' ? 'service-daily' : 'service-hourly'}">
                                            ${inv.serviceType === 'daily' ? 'إيجار 24 ساعة' : 'بالساعة'}
                                        </span>
                                    </div>
                                    <div class="invoice-amount">${inv.finalAmount} جنيه</div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في عرض الفواتير: ${error.message}`;
            }
        }

        // حذف جميع الفواتير
        function clearAllInvoices() {
            if (!confirm('هل أنت متأكد من حذف جميع الفواتير؟')) return;
            
            const result = document.getElementById('invoiceResult');
            result.style.display = 'block';
            
            try {
                const keysToDelete = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('invoice_')) {
                        keysToDelete.push(key);
                    }
                }

                keysToDelete.forEach(key => localStorage.removeItem(key));

                result.className = 'result success';
                result.innerHTML = `✅ تم حذف ${keysToDelete.length} فاتورة بنجاح.`;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في حذف الفواتير: ${error.message}`;
            }
        }

        // اختبار الخدمة بالساعة
        function testHourlyService() {
            const result = document.getElementById('pricingResult');
            result.style.display = 'block';
            
            const hourlyRate = 10; // Default rate
            const hours = 2.5;
            const cost = hours * hourlyRate;
            
            result.className = 'result success';
            result.innerHTML = `
                ✅ اختبار الخدمة بالساعة:<br>
                - السعر: ${hourlyRate} جنيه/ساعة<br>
                - الوقت: ${hours} ساعة<br>
                - التكلفة: ${cost} جنيه
            `;
        }

        // اختبار إيجار 24 ساعة
        function testDailyService() {
            const result = document.getElementById('pricingResult');
            result.style.display = 'block';
            
            const dailyRate = 65; // Default rate
            
            result.className = 'result success';
            result.innerHTML = `
                ✅ اختبار إيجار 24 ساعة:<br>
                - السعر: ${dailyRate} جنيه/يوم<br>
                - مدة الإيجار: 24 ساعة<br>
                - التكلفة الثابتة: ${dailyRate} جنيه
            `;
        }

        // تحديث إعدادات الأسعار
        function updatePriceSettings() {
            const result = document.getElementById('pricingResult');
            result.style.display = 'block';
            
            try {
                // حفظ الإعدادات في localStorage
                localStorage.setItem('setting_hourlyRate', JSON.stringify({key: 'hourlyRate', value: 10}));
                localStorage.setItem('setting_dailyRate', JSON.stringify({key: 'dailyRate', value: 65}));
                localStorage.setItem('setting_placeName', JSON.stringify({key: 'placeName', value: 'سكون'}));
                
                result.className = 'result success';
                result.innerHTML = `
                    ✅ تم تحديث إعدادات الأسعار:<br>
                    - اسم المكان: سكون<br>
                    - سعر الساعة: 10 جنيه<br>
                    - سعر إيجار 24 ساعة: 65 جنيه
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في تحديث الإعدادات: ${error.message}`;
            }
        }

        // فتح النظام الرئيسي
        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            updatePriceSettings();
        };
    </script>
</body>
</html>
