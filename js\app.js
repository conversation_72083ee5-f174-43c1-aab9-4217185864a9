// Main Application Controller
class CafeApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    async init() {
        // Wait for storage to initialize
        await this.waitForStorage();

        // Restore from emergency backup if needed
        await this.restoreFromEmergencyBackup();

        // Initialize theme
        this.initTheme();

        // Setup event listeners
        this.setupEventListeners();

        // Initialize activity manager
        this.initActivityManager();

        // Show initial section
        this.showSection('dashboard');

        // Initialize settings
        await this.initSettings();

        // Start auto-save system
        this.startAutoSave();

        console.log('Study Place Management App initialized successfully');
    }

    // Wait for storage to be ready
    async waitForStorage() {
        let attempts = 0;
        while (!window.storage && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (!window.storage) {
            throw new Error('Storage not available');
        }
    }

    // Initialize theme
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        this.updateThemeIcon();
    }

    // Setup event listeners
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Modal overlay click to close
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // Form submissions
        this.setupFormSubmissions();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window beforeunload - save data before closing
        window.addEventListener('beforeunload', async (e) => {
            // Always save data before closing
            await this.saveAllDataBeforeExit();

            if (customerManager && customerManager.activeCustomers.size > 0) {
                e.preventDefault();
                e.returnValue = 'يوجد عملاء نشطين. هل أنت متأكد من الخروج؟';
            }
        });

        // Handle page visibility change (when switching tabs)
        document.addEventListener('visibilitychange', async () => {
            if (document.hidden) {
                await this.saveAllDataBeforeExit();
            }
        });
    }

    // Setup form submissions
    setupFormSubmissions() {
        // Customer form
        const customerForm = document.getElementById('customerForm');
        if (customerForm) {
            customerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCustomerFormSubmit();
            });
        }

        // Product form
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProductFormSubmit();
            });
        }
    }

    // Handle customer form submit
    async handleCustomerFormSubmit() {
        try {
            const customerId = document.getElementById('customerForm').dataset.customerId;
            const formData = {
                name: document.getElementById('customerName').value,
                phone: document.getElementById('customerPhone').value,
                notes: document.getElementById('customerNotes').value,
                serviceType: document.getElementById('serviceType').value
            };

            if (!formData.name.trim()) {
                alert('يرجى إدخال اسم العميل');
                return;
            }

            if (customerId) {
                // Update existing customer
                await customerManager.updateCustomer(customerId, formData);
            } else {
                // Add new customer
                await customerManager.addCustomer(formData);
            }

            this.closeModal();
            this.clearForm('customerForm');
        } catch (error) {
            alert('حدث خطأ: ' + error.message);
        }
    }

    // Handle product form submit
    async handleProductFormSubmit() {
        try {
            const productId = document.getElementById('productForm').dataset.productId;
            const formData = {
                name: document.getElementById('productName').value,
                code: document.getElementById('productCode').value,
                price: document.getElementById('productPrice').value,
                category: document.getElementById('productCategory').value
            };

            if (!formData.name.trim() || !formData.code.trim() || !formData.price) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (productId) {
                // Update existing product
                await productManager.updateProduct(productId, formData);
            } else {
                // Add new product
                await productManager.addProduct(formData);
            }

            this.closeModal();
            this.clearForm('productForm');
        } catch (error) {
            alert('حدث خطأ: ' + error.message);
        }
    }

    // Show section
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionName;

        // Update URL hash
        window.location.hash = sectionName;
    }

    // Toggle theme
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('theme', this.theme);
        this.updateThemeIcon();
    }

    // Update theme icon
    updateThemeIcon() {
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Show modal
    showModal(modalId = null) {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            if (modalId) {
                // Hide all modals first
                modalOverlay.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
                
                // Show specific modal
                const targetModal = document.getElementById(modalId);
                if (targetModal) {
                    targetModal.style.display = 'block';
                }
            }
            
            modalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close modal
    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Clear any form data
            modalOverlay.querySelectorAll('form').forEach(form => {
                this.clearForm(form.id);
            });
        }
    }

    // Clear form
    clearForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            // Remove any dataset attributes
            delete form.dataset.productId;
            delete form.dataset.customerId;
        }
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.showSection('customers');
                    break;
                case '3':
                    e.preventDefault();
                    this.showSection('products');
                    break;
                case '4':
                    e.preventDefault();
                    this.showSection('invoices');
                    break;
                case 'n':
                    e.preventDefault();
                    if (this.currentSection === 'customers') {
                        this.showAddCustomerModal();
                    } else if (this.currentSection === 'products') {
                        this.showAddProductModal();
                    }
                    break;
            }
        }

        // Escape key to close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }
    }

    // Initialize activity manager
    initActivityManager() {
        window.activityManager = {
            activities: [],
            maxActivities: 10,
            
            addActivity(message) {
                const activity = {
                    id: Date.now(),
                    message: message,
                    timestamp: new Date().toISOString()
                };
                
                this.activities.unshift(activity);
                
                // Keep only the latest activities
                if (this.activities.length > this.maxActivities) {
                    this.activities = this.activities.slice(0, this.maxActivities);
                }
                
                this.renderActivities();
            },
            
            renderActivities() {
                const activityList = document.getElementById('recentActivityList');
                if (!activityList) return;
                
                if (this.activities.length === 0) {
                    activityList.innerHTML = '<p class="no-activity">لا توجد أنشطة حديثة</p>';
                    return;
                }
                
                activityList.innerHTML = this.activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-message">${activity.message}</div>
                        <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
                    </div>
                `).join('');
            },
            
            formatTime(timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diffMs = now - date;
                const diffMins = Math.floor(diffMs / 60000);
                
                if (diffMins < 1) return 'الآن';
                if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
                if (diffMins < 1440) return `منذ ${Math.floor(diffMins / 60)} ساعة`;
                return date.toLocaleDateString('ar-EG');
            }
        };
    }

    // Initialize settings
    async initSettings() {
        try {
            // Set default settings if they don't exist
            const defaultSettings = [
                { key: 'placeName', value: 'سكون' },
                { key: 'hourlyRate', value: 10 },
                { key: 'dailyRate', value: 65 },
                { key: 'currency', value: 'جنيه' },
                { key: 'taxRate', value: 0 }
            ];

            for (const setting of defaultSettings) {
                const existing = await storage.get('settings', setting.key);
                if (!existing) {
                    await storage.save('settings', setting);
                }
            }

            // Load settings into UI
            this.loadSettingsUI();
        } catch (error) {
            console.error('Error initializing settings:', error);
        }
    }

    // Load settings into UI
    async loadSettingsUI() {
        try {
            const placeName = await storage.get('settings', 'placeName');
            const hourlyRate = await storage.get('settings', 'hourlyRate');

            if (placeName) {
                const placeNameInput = document.getElementById('placeName');
                if (placeNameInput) placeNameInput.value = placeName.value;
            }

            if (hourlyRate) {
                const hourlyRateInput = document.getElementById('hourlyRate');
                if (hourlyRateInput) hourlyRateInput.value = hourlyRate.value;
            }

            const dailyRate = await storage.get('settings', 'dailyRate');
            if (dailyRate) {
                const dailyRateInput = document.getElementById('dailyRate');
                if (dailyRateInput) dailyRateInput.value = dailyRate.value;
            }
        } catch (error) {
            console.error('Error loading settings UI:', error);
        }
    }

    // Save settings
    async saveSettings() {
        try {
            const placeName = document.getElementById('placeName').value;
            const hourlyRate = document.getElementById('hourlyRate').value;
            const dailyRate = document.getElementById('dailyRate').value;

            await storage.save('settings', { key: 'placeName', value: placeName });
            await storage.save('settings', { key: 'hourlyRate', value: parseFloat(hourlyRate) });
            await storage.save('settings', { key: 'dailyRate', value: parseFloat(dailyRate) });

            alert('تم حفظ الإعدادات بنجاح');
        } catch (error) {
            alert('حدث خطأ أثناء حفظ الإعدادات');
            console.error('Error saving settings:', error);
        }
    }

    // Show add customer modal
    showAddCustomerModal() {
        this.clearForm('customerForm');
        this.showModal('addCustomerModal');
        // تحديث معلومات الخدمة عند فتح النموذج
        updateServiceInfo();
    }

    // Show add product modal
    showAddProductModal() {
        this.clearForm('productForm');
        this.showModal('addProductModal');
    }

    // Export all data
    async exportData() {
        try {
            const data = await storage.exportData();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cafe_backup_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            alert('تم تصدير البيانات بنجاح');
        } catch (error) {
            alert('حدث خطأ أثناء تصدير البيانات');
            console.error('Export error:', error);
        }
    }

    // Import data
    async importData() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = async (e) => {
                    try {
                        const data = e.target.result;
                        await storage.importData(data);

                        alert('تم استيراد البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
                        window.location.reload();
                    } catch (error) {
                        alert('حدث خطأ أثناء استيراد البيانات: ' + error.message);
                    }
                };
                reader.readAsText(file);
            };

            input.click();
        } catch (error) {
            alert('حدث خطأ أثناء استيراد البيانات');
            console.error('Import error:', error);
        }
    }

    // Save all data before exit
    async saveAllDataBeforeExit() {
        try {
            // Save current shift if exists
            if (window.shiftManager && window.shiftManager.currentShift) {
                await window.shiftManager.saveCurrentShiftState();
            }

            // Update active customers with current time and cost
            if (window.customerManager && window.customerManager.activeCustomers.size > 0) {
                for (const customer of window.customerManager.activeCustomers) {
                    const currentTime = window.customerManager.calculateCurrentTime(customer.entryTime);
                    const hourlyRate = await window.customerManager.getHourlyRate();
                    const timeCost = (currentTime / 60) * hourlyRate;
                    const purchasesCost = customer.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
                    const totalCost = timeCost + purchasesCost;

                    await window.customerManager.updateCustomer(customer.id, {
                        totalTime: currentTime,
                        totalCost: totalCost,
                        updatedAt: new Date().toISOString()
                    });
                }
            }

            // Force save all data to storage
            await this.createEmergencyBackup();

            console.log('All data saved before exit');
        } catch (error) {
            console.error('Error saving data before exit:', error);
        }
    }

    // Create emergency backup
    async createEmergencyBackup() {
        try {
            const emergencyData = {
                timestamp: new Date().toISOString(),
                customers: await storage.getAll('customers'),
                products: await storage.getAll('products'),
                invoices: await storage.getAll('invoices'),
                shifts: await storage.getAll('shifts'),
                settings: await storage.getAll('settings'),
                currentShift: window.shiftManager ? window.shiftManager.currentShift : null
            };

            // Save to localStorage as emergency backup
            localStorage.setItem('emergencyBackup', JSON.stringify(emergencyData));
            localStorage.setItem('emergencyBackupTime', new Date().toISOString());

            console.log('Emergency backup created');
        } catch (error) {
            console.error('Error creating emergency backup:', error);
        }
    }

    // Restore from emergency backup if needed
    async restoreFromEmergencyBackup() {
        try {
            const emergencyBackup = localStorage.getItem('emergencyBackup');
            const backupTime = localStorage.getItem('emergencyBackupTime');

            if (emergencyBackup && backupTime) {
                const backupDate = new Date(backupTime);
                const now = new Date();
                const hoursDiff = (now - backupDate) / (1000 * 60 * 60);

                // Only restore if backup is less than 24 hours old
                if (hoursDiff < 24) {
                    const data = JSON.parse(emergencyBackup);

                    // Check if current data is missing or corrupted
                    const currentCustomers = await storage.getAll('customers');
                    const currentShifts = await storage.getAll('shifts');

                    if (currentCustomers.length === 0 && data.customers.length > 0) {
                        // Restore customers
                        for (const customer of data.customers) {
                            await storage.save('customers', customer);
                        }
                        console.log('Restored customers from emergency backup');
                    }

                    if (currentShifts.length === 0 && data.shifts.length > 0) {
                        // Restore shifts
                        for (const shift of data.shifts) {
                            await storage.save('shifts', shift);
                        }
                        console.log('Restored shifts from emergency backup');
                    }

                    // Restore current shift if it was active
                    if (data.currentShift && data.currentShift.status === 'active') {
                        await storage.save('shifts', data.currentShift);
                        await storage.save('settings', { key: 'currentShift', value: data.currentShift.id });
                        console.log('Restored active shift from emergency backup');
                    }
                }
            }
        } catch (error) {
            console.error('Error restoring from emergency backup:', error);
        }
    }

    // Safe exit function
    async safeExit() {
        const exitModal = `
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-power-off"></i> إغلاق البرنامج بأمان</h3>
                </div>
                <div class="modal-body">
                    <div class="exit-info">
                        <div class="exit-status" id="exitStatus">
                            <i class="fas fa-spinner fa-spin"></i> جاري حفظ البيانات...
                        </div>
                        <div class="exit-details" id="exitDetails">
                            <p>يتم الآن حفظ جميع البيانات بأمان:</p>
                            <ul id="exitProgress">
                                <li id="saveCustomers"><i class="fas fa-clock"></i> حفظ بيانات العملاء...</li>
                                <li id="saveShifts"><i class="fas fa-clock"></i> حفظ بيانات الشيفتات...</li>
                                <li id="saveProducts"><i class="fas fa-clock"></i> حفظ بيانات المنتجات...</li>
                                <li id="createBackup"><i class="fas fa-clock"></i> إنشاء نسخة احتياطية...</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()" id="cancelExit">إلغاء</button>
                    <button class="btn btn-danger" onclick="app.forceExit()" id="forceExit" style="display: none;">
                        <i class="fas fa-power-off"></i> إغلاق فوري
                    </button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = exitModal;
        this.showModal();

        // Start safe exit process
        await this.performSafeExit();
    }

    // Perform safe exit process
    async performSafeExit() {
        try {
            // Save customers
            document.getElementById('saveCustomers').innerHTML = '<i class="fas fa-spinner fa-spin"></i> حفظ بيانات العملاء...';
            await this.saveAllDataBeforeExit();
            document.getElementById('saveCustomers').innerHTML = '<i class="fas fa-check text-success"></i> تم حفظ بيانات العملاء';

            await new Promise(resolve => setTimeout(resolve, 500));

            // Save shifts
            document.getElementById('saveShifts').innerHTML = '<i class="fas fa-spinner fa-spin"></i> حفظ بيانات الشيفتات...';
            if (window.shiftManager) {
                await window.shiftManager.saveCurrentShiftState();
            }
            document.getElementById('saveShifts').innerHTML = '<i class="fas fa-check text-success"></i> تم حفظ بيانات الشيفتات';

            await new Promise(resolve => setTimeout(resolve, 500));

            // Save products
            document.getElementById('saveProducts').innerHTML = '<i class="fas fa-spinner fa-spin"></i> حفظ بيانات المنتجات...';
            // Products are already saved automatically
            document.getElementById('saveProducts').innerHTML = '<i class="fas fa-check text-success"></i> تم حفظ بيانات المنتجات';

            await new Promise(resolve => setTimeout(resolve, 500));

            // Create backup
            document.getElementById('createBackup').innerHTML = '<i class="fas fa-spinner fa-spin"></i> إنشاء نسخة احتياطية...';
            await this.createEmergencyBackup();
            document.getElementById('createBackup').innerHTML = '<i class="fas fa-check text-success"></i> تم إنشاء النسخة الاحتياطية';

            await new Promise(resolve => setTimeout(resolve, 1000));

            // Show completion
            document.getElementById('exitStatus').innerHTML = '<i class="fas fa-check-circle text-success"></i> تم حفظ جميع البيانات بنجاح!';
            document.getElementById('exitDetails').innerHTML = `
                <div class="alert alert-success">
                    <h4><i class="fas fa-shield-check"></i> تم الحفظ بأمان</h4>
                    <p>جميع البيانات محفوظة بأمان. يمكنك الآن إغلاق البرنامج بثقة.</p>
                    <p><strong>ملاحظة:</strong> عند فتح البرنامج مرة أخرى، ستجد جميع بياناتك كما تركتها.</p>
                </div>
            `;

            // Show exit options
            document.getElementById('cancelExit').textContent = 'البقاء في البرنامج';
            document.getElementById('forceExit').style.display = 'inline-block';
            document.getElementById('forceExit').innerHTML = '<i class="fas fa-power-off"></i> إغلاق البرنامج';
            document.getElementById('forceExit').className = 'btn btn-success';

        } catch (error) {
            console.error('Error during safe exit:', error);
            document.getElementById('exitStatus').innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> حدث خطأ أثناء الحفظ';
            document.getElementById('exitDetails').innerHTML = `
                <div class="alert alert-warning">
                    <p>حدث خطأ أثناء حفظ البيانات. يمكنك المحاولة مرة أخرى أو الإغلاق الفوري.</p>
                    <p><strong>تحذير:</strong> الإغلاق الفوري قد يؤدي لفقدان بعض البيانات غير المحفوظة.</p>
                </div>
            `;
            document.getElementById('forceExit').style.display = 'inline-block';
        }
    }

    // Force exit
    forceExit() {
        if (confirm('هل أنت متأكد من الإغلاق الفوري؟ قد تفقد بعض البيانات غير المحفوظة.')) {
            window.close();
            // If window.close() doesn't work, redirect to blank page
            setTimeout(() => {
                window.location.href = 'about:blank';
            }, 100);
        }
    }

    // Start auto-save system
    startAutoSave() {
        // Auto-save every 2 minutes
        setInterval(async () => {
            await this.saveAllDataBeforeExit();
            console.log('Auto-save completed');
        }, 2 * 60 * 1000); // Every 2 minutes

        // Create emergency backup every 5 minutes
        setInterval(async () => {
            await this.createEmergencyBackup();
            console.log('Emergency backup created');
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    // Show data recovery notification if needed
    showDataRecoveryNotification() {
        const emergencyBackup = localStorage.getItem('emergencyBackup');
        const backupTime = localStorage.getItem('emergencyBackupTime');

        if (emergencyBackup && backupTime) {
            const backupDate = new Date(backupTime);
            const now = new Date();
            const minutesDiff = (now - backupDate) / (1000 * 60);

            if (minutesDiff < 60) { // If backup is less than 1 hour old
                this.showNotification(
                    `تم استعادة البيانات من النسخة الاحتياطية (${Math.round(minutesDiff)} دقيقة مضت)`,
                    'success',
                    8000
                );
            }
        }
    }

    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification container if it doesn't exist
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    // Get notification icon
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions for HTML onclick events
window.showModal = (modalId) => app.showModal(modalId);
window.closeModal = () => app.closeModal();
window.showAddCustomerModal = () => app.showAddCustomerModal();
window.showAddProductModal = () => app.showAddProductModal();
window.addCustomer = () => app.handleCustomerFormSubmit();
window.addProduct = () => app.handleProductFormSubmit();
window.exportData = () => app.exportData();
window.importData = () => app.importData();
window.openShift = () => shiftManager.showStartShiftModal();
window.closeShift = () => shiftManager.showEndShiftModal();
window.showSection = (section) => app.showSection(section);

// Update service info based on selected service type
async function updateServiceInfo() {
    const serviceType = document.getElementById('serviceType');
    const serviceInfo = document.getElementById('serviceRate');

    if (!serviceType || !serviceInfo) return;

    try {
        if (serviceType.value === 'hourly') {
            const hourlyRate = await storage.get('settings', 'hourlyRate');
            const rate = hourlyRate ? hourlyRate.value : 10;
            serviceInfo.textContent = `${rate} جنيه/ساعة`;
        } else if (serviceType.value === 'daily') {
            const dailyRate = await storage.get('settings', 'dailyRate');
            const rate = dailyRate ? dailyRate.value : 65;
            serviceInfo.textContent = `${rate} جنيه/24 ساعة`;
        }
    } catch (error) {
        console.error('Error updating service info:', error);
        serviceInfo.textContent = serviceType.value === 'hourly' ? '10 جنيه/ساعة' : '65 جنيه/24 ساعة';
    }
}

// Get daily rate from settings
async function getDailyRate() {
    try {
        const setting = await storage.get('settings', 'dailyRate');
        return setting ? setting.value : 65; // Default 65 EGP per day
    } catch (error) {
        return 65;
    }
}

// Reset database function
async function resetDatabase() {
    const confirmed = confirm(
        'هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\n\n' +
        'سيتم حذف جميع البيانات:\n' +
        '- العملاء والجلسات\n' +
        '- الفواتير\n' +
        '- الموظفين\n' +
        '- المنتجات\n' +
        '- الشيفتات\n\n' +
        'هذا الإجراء لا يمكن التراجع عنه!'
    );

    if (confirmed) {
        try {
            await storage.resetDatabase();
            alert('تم إعادة تعيين قاعدة البيانات بنجاح!\n\nسيتم إعادة تحميل الصفحة...');
            window.location.reload();
        } catch (error) {
            alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات: ' + error.message);
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CafeApp();
});

// Handle hash changes for navigation
window.addEventListener('hashchange', () => {
    const hash = window.location.hash.substring(1);
    if (hash && window.app) {
        window.app.showSection(hash);
    }
});

// Set initial hash if none exists
if (!window.location.hash) {
    window.location.hash = 'dashboard';
}
