// Main Application Controller
class CafeApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    async init() {
        // Wait for storage to initialize
        await this.waitForStorage();
        
        // Initialize theme
        this.initTheme();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize activity manager
        this.initActivityManager();
        
        // Show initial section
        this.showSection('dashboard');
        
        // Initialize settings
        await this.initSettings();
        
        console.log('Cafe Management App initialized successfully');
    }

    // Wait for storage to be ready
    async waitForStorage() {
        let attempts = 0;
        while (!window.storage && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (!window.storage) {
            throw new Error('Storage not available');
        }
    }

    // Initialize theme
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        this.updateThemeIcon();
    }

    // Setup event listeners
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Modal overlay click to close
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // Form submissions
        this.setupFormSubmissions();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window beforeunload
        window.addEventListener('beforeunload', (e) => {
            if (customerManager && customerManager.activeCustomers.size > 0) {
                e.preventDefault();
                e.returnValue = 'يوجد عملاء نشطين. هل أنت متأكد من الخروج؟';
            }
        });
    }

    // Setup form submissions
    setupFormSubmissions() {
        // Customer form
        const customerForm = document.getElementById('customerForm');
        if (customerForm) {
            customerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCustomerFormSubmit();
            });
        }

        // Product form
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProductFormSubmit();
            });
        }
    }

    // Handle customer form submit
    async handleCustomerFormSubmit() {
        try {
            const customerId = document.getElementById('customerForm').dataset.customerId;
            const formData = {
                name: document.getElementById('customerName').value,
                phone: document.getElementById('customerPhone').value,
                notes: document.getElementById('customerNotes').value
            };

            if (!formData.name.trim()) {
                alert('يرجى إدخال اسم العميل');
                return;
            }

            if (customerId) {
                // Update existing customer
                await customerManager.updateCustomer(customerId, formData);
            } else {
                // Add new customer
                await customerManager.addCustomer(formData);
            }

            this.closeModal();
            this.clearForm('customerForm');
        } catch (error) {
            alert('حدث خطأ: ' + error.message);
        }
    }

    // Handle product form submit
    async handleProductFormSubmit() {
        try {
            const productId = document.getElementById('productForm').dataset.productId;
            const formData = {
                name: document.getElementById('productName').value,
                code: document.getElementById('productCode').value,
                price: document.getElementById('productPrice').value,
                category: document.getElementById('productCategory').value
            };

            if (!formData.name.trim() || !formData.code.trim() || !formData.price) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (productId) {
                // Update existing product
                await productManager.updateProduct(productId, formData);
            } else {
                // Add new product
                await productManager.addProduct(formData);
            }

            this.closeModal();
            this.clearForm('productForm');
        } catch (error) {
            alert('حدث خطأ: ' + error.message);
        }
    }

    // Show section
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionName;

        // Update URL hash
        window.location.hash = sectionName;
    }

    // Toggle theme
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('theme', this.theme);
        this.updateThemeIcon();
    }

    // Update theme icon
    updateThemeIcon() {
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Show modal
    showModal(modalId = null) {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            if (modalId) {
                // Hide all modals first
                modalOverlay.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
                
                // Show specific modal
                const targetModal = document.getElementById(modalId);
                if (targetModal) {
                    targetModal.style.display = 'block';
                }
            }
            
            modalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close modal
    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Clear any form data
            modalOverlay.querySelectorAll('form').forEach(form => {
                this.clearForm(form.id);
            });
        }
    }

    // Clear form
    clearForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            // Remove any dataset attributes
            delete form.dataset.productId;
            delete form.dataset.customerId;
        }
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.showSection('customers');
                    break;
                case '3':
                    e.preventDefault();
                    this.showSection('products');
                    break;
                case '4':
                    e.preventDefault();
                    this.showSection('invoices');
                    break;
                case 'n':
                    e.preventDefault();
                    if (this.currentSection === 'customers') {
                        this.showAddCustomerModal();
                    } else if (this.currentSection === 'products') {
                        this.showAddProductModal();
                    }
                    break;
            }
        }

        // Escape key to close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }
    }

    // Initialize activity manager
    initActivityManager() {
        window.activityManager = {
            activities: [],
            maxActivities: 10,
            
            addActivity(message) {
                const activity = {
                    id: Date.now(),
                    message: message,
                    timestamp: new Date().toISOString()
                };
                
                this.activities.unshift(activity);
                
                // Keep only the latest activities
                if (this.activities.length > this.maxActivities) {
                    this.activities = this.activities.slice(0, this.maxActivities);
                }
                
                this.renderActivities();
            },
            
            renderActivities() {
                const activityList = document.getElementById('recentActivityList');
                if (!activityList) return;
                
                if (this.activities.length === 0) {
                    activityList.innerHTML = '<p class="no-activity">لا توجد أنشطة حديثة</p>';
                    return;
                }
                
                activityList.innerHTML = this.activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-message">${activity.message}</div>
                        <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
                    </div>
                `).join('');
            },
            
            formatTime(timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diffMs = now - date;
                const diffMins = Math.floor(diffMs / 60000);
                
                if (diffMins < 1) return 'الآن';
                if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
                if (diffMins < 1440) return `منذ ${Math.floor(diffMins / 60)} ساعة`;
                return date.toLocaleDateString('ar-EG');
            }
        };
    }

    // Initialize settings
    async initSettings() {
        try {
            // Set default settings if they don't exist
            const defaultSettings = [
                { key: 'placeName', value: 'مكان المذاكرة' },
                { key: 'hourlyRate', value: 10 },
                { key: 'currency', value: 'جنيه' },
                { key: 'taxRate', value: 0 }
            ];

            for (const setting of defaultSettings) {
                const existing = await storage.get('settings', setting.key);
                if (!existing) {
                    await storage.save('settings', setting);
                }
            }

            // Load settings into UI
            this.loadSettingsUI();
        } catch (error) {
            console.error('Error initializing settings:', error);
        }
    }

    // Load settings into UI
    async loadSettingsUI() {
        try {
            const placeName = await storage.get('settings', 'placeName');
            const hourlyRate = await storage.get('settings', 'hourlyRate');

            if (placeName) {
                const placeNameInput = document.getElementById('placeName');
                if (placeNameInput) placeNameInput.value = placeName.value;
            }

            if (hourlyRate) {
                const hourlyRateInput = document.getElementById('hourlyRate');
                if (hourlyRateInput) hourlyRateInput.value = hourlyRate.value;
            }
        } catch (error) {
            console.error('Error loading settings UI:', error);
        }
    }

    // Save settings
    async saveSettings() {
        try {
            const placeName = document.getElementById('placeName').value;
            const hourlyRate = document.getElementById('hourlyRate').value;

            await storage.save('settings', { key: 'placeName', value: placeName });
            await storage.save('settings', { key: 'hourlyRate', value: parseFloat(hourlyRate) });

            alert('تم حفظ الإعدادات بنجاح');
        } catch (error) {
            alert('حدث خطأ أثناء حفظ الإعدادات');
            console.error('Error saving settings:', error);
        }
    }

    // Show add customer modal
    showAddCustomerModal() {
        this.clearForm('customerForm');
        this.showModal('addCustomerModal');
    }

    // Show add product modal
    showAddProductModal() {
        this.clearForm('productForm');
        this.showModal('addProductModal');
    }

    // Export all data
    async exportData() {
        try {
            const data = await storage.exportData();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cafe_backup_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            alert('تم تصدير البيانات بنجاح');
        } catch (error) {
            alert('حدث خطأ أثناء تصدير البيانات');
            console.error('Export error:', error);
        }
    }

    // Import data
    async importData() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = async (e) => {
                    try {
                        const data = e.target.result;
                        await storage.importData(data);
                        
                        alert('تم استيراد البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
                        window.location.reload();
                    } catch (error) {
                        alert('حدث خطأ أثناء استيراد البيانات: ' + error.message);
                    }
                };
                reader.readAsText(file);
            };
            
            input.click();
        } catch (error) {
            alert('حدث خطأ أثناء استيراد البيانات');
            console.error('Import error:', error);
        }
    }
}

// Global functions for HTML onclick events
window.showModal = (modalId) => app.showModal(modalId);
window.closeModal = () => app.closeModal();
window.showAddCustomerModal = () => app.showAddCustomerModal();
window.showAddProductModal = () => app.showAddProductModal();
window.addCustomer = () => app.handleCustomerFormSubmit();
window.addProduct = () => app.handleProductFormSubmit();
window.exportData = () => app.exportData();
window.importData = () => app.importData();
window.openShift = () => shiftManager.showStartShiftModal();
window.closeShift = () => shiftManager.showEndShiftModal();
window.showSection = (section) => app.showSection(section);

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CafeApp();
});

// Handle hash changes for navigation
window.addEventListener('hashchange', () => {
    const hash = window.location.hash.substring(1);
    if (hash && window.app) {
        window.app.showSection(hash);
    }
});

// Set initial hash if none exists
if (!window.location.hash) {
    window.location.hash = 'dashboard';
}
