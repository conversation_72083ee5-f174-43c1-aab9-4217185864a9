// Shift Management System
class ShiftManager {
    constructor() {
        this.shifts = [];
        this.currentShift = null;
        this.init();
    }

    async init() {
        await this.loadShifts();
        await this.loadCurrentShift();
        await this.restoreFromBackup(); // Restore any lost data
        this.renderShifts();
        this.updateShiftStatus();
        this.setupEventListeners();
        this.startAutoSave(); // Start auto-save timer
    }

    // Load shifts from storage
    async loadShifts() {
        try {
            this.shifts = await storage.getAll('shifts');

            // Add shift codes to old shifts that don't have them
            let needsUpdate = false;
            for (let shift of this.shifts) {
                if (!shift.shiftCode) {
                    const shiftDate = new Date(shift.startTime);
                    const datePrefix = shiftDate.getFullYear().toString().substr(-2) +
                                      (shiftDate.getMonth() + 1).toString().padStart(2, '0') +
                                      shiftDate.getDate().toString().padStart(2, '0');

                    // Find sequence number for this date
                    const sameDateShifts = this.shifts.filter(s => {
                        const sDate = new Date(s.startTime);
                        return sDate.toDateString() === shiftDate.toDateString() && s.id !== shift.id;
                    });

                    const sequence = (sameDateShifts.length + 1).toString().padStart(3, '0');
                    shift.shiftCode = `SH-${datePrefix}-${sequence}`;

                    await storage.save('shifts', shift);
                    needsUpdate = true;
                }
            }

            if (needsUpdate) {
                this.shifts = await storage.getAll('shifts');
            }

            // Sort by start time (newest first)
            this.shifts.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
        } catch (error) {
            console.error('Error loading shifts:', error);
            this.shifts = [];
        }
    }

    // Load current shift
    async loadCurrentShift() {
        try {
            const currentShiftData = await storage.get('settings', 'currentShift');
            if (currentShiftData && currentShiftData.value) {
                this.currentShift = await storage.get('shifts', currentShiftData.value);

                // If current shift exists but not in shifts array, add it
                if (this.currentShift && !this.shifts.find(s => s.id === this.currentShift.id)) {
                    this.shifts.unshift(this.currentShift);
                }
            }
        } catch (error) {
            console.error('Error loading current shift:', error);
            this.currentShift = null;
        }
    }

    // Save current shift state to ensure persistence
    async saveCurrentShiftState() {
        try {
            if (this.currentShift) {
                await storage.save('shifts', this.currentShift);
                await storage.save('settings', { key: 'currentShift', value: this.currentShift.id });

                // Also save to localStorage as backup
                localStorage.setItem('currentShiftBackup', JSON.stringify(this.currentShift));
                localStorage.setItem('shiftsBackup', JSON.stringify(this.shifts));
            }
        } catch (error) {
            console.error('Error saving current shift state:', error);
        }
    }

    // Restore from backup if needed
    async restoreFromBackup() {
        try {
            if (!this.currentShift) {
                const backupShift = localStorage.getItem('currentShiftBackup');
                const backupShifts = localStorage.getItem('shiftsBackup');

                if (backupShift) {
                    this.currentShift = JSON.parse(backupShift);
                    await storage.save('shifts', this.currentShift);
                    await storage.save('settings', { key: 'currentShift', value: this.currentShift.id });
                }

                if (backupShifts) {
                    const shifts = JSON.parse(backupShifts);
                    for (const shift of shifts) {
                        const existing = await storage.get('shifts', shift.id);
                        if (!existing) {
                            await storage.save('shifts', shift);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error restoring from backup:', error);
        }
    }

    // Generate shift code
    async generateShiftCode() {
        const today = new Date();
        const datePrefix = today.getFullYear().toString().substr(-2) +
                          (today.getMonth() + 1).toString().padStart(2, '0') +
                          today.getDate().toString().padStart(2, '0');

        // Get today's shifts count
        const todayShifts = this.shifts.filter(shift => {
            const shiftDate = new Date(shift.startTime);
            return shiftDate.toDateString() === today.toDateString();
        });

        const sequence = (todayShifts.length + 1).toString().padStart(3, '0');
        return `SH-${datePrefix}-${sequence}`;
    }

    // Start new shift
    async startShift(employeeName = 'المدير', openingCash = 0) {
        try {
            if (this.currentShift) {
                throw new Error('يوجد شيفت مفتوح بالفعل. يجب إغلاق الشيفت الحالي أولاً.');
            }

            const shiftCode = await this.generateShiftCode();
            const shift = {
                id: storage.generateId(),
                shiftCode: shiftCode,
                employeeName: employeeName.trim(),
                startTime: new Date().toISOString(),
                endTime: null,
                openingCash: parseFloat(openingCash) || 0,
                closingCash: 0,
                totalSales: 0,
                totalInvoices: 0,
                expenses: [],
                totalExpenses: 0,
                netAmount: 0,
                status: 'active',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Save shift to storage
            await storage.save('shifts', shift);
            await storage.save('settings', { key: 'currentShift', value: shift.id });

            // Update local arrays
            this.shifts.unshift(shift);
            this.currentShift = shift;

            // Force save to ensure persistence
            await this.saveCurrentShiftState();

            this.renderShifts();
            this.updateShiftStatus();
            this.addActivity(`تم فتح شيفت جديد - كود: ${shiftCode} - ${employeeName}`);

            return shift;
        } catch (error) {
            console.error('Error starting shift:', error);
            throw error;
        }
    }

    // End current shift
    async endShift(closingCash = 0, notes = '') {
        try {
            if (!this.currentShift) {
                throw new Error('لا يوجد شيفت مفتوح حالياً');
            }

            // Calculate shift statistics
            const shiftStats = await this.calculateShiftStats(this.currentShift.id);
            
            const updatedShift = {
                ...this.currentShift,
                endTime: new Date().toISOString(),
                closingCash: parseFloat(closingCash) || 0,
                totalSales: shiftStats.totalSales,
                totalInvoices: shiftStats.totalInvoices,
                totalExpenses: shiftStats.totalExpenses,
                netAmount: shiftStats.totalSales - shiftStats.totalExpenses,
                notes: notes.trim(),
                status: 'completed',
                updatedAt: new Date().toISOString()
            };

            await storage.save('shifts', updatedShift);
            await storage.delete('settings', 'currentShift');

            // Clear backup data
            localStorage.removeItem('currentShiftBackup');

            // Update shifts array
            const shiftIndex = this.shifts.findIndex(s => s.id === this.currentShift.id);
            if (shiftIndex !== -1) {
                this.shifts[shiftIndex] = updatedShift;
            }

            // Force save shifts array
            localStorage.setItem('shiftsBackup', JSON.stringify(this.shifts));

            this.currentShift = null;

            this.renderShifts();
            this.updateShiftStatus();
            this.addActivity(`تم إغلاق الشيفت - كود: ${updatedShift.shiftCode} - صافي المبيعات: ${updatedShift.netAmount.toFixed(2)} جنيه`);

            return updatedShift;
        } catch (error) {
            console.error('Error ending shift:', error);
            throw error;
        }
    }

    // Calculate shift statistics
    async calculateShiftStats(shiftId) {
        const shift = this.shifts.find(s => s.id === shiftId) || this.currentShift;
        if (!shift) return { totalSales: 0, totalInvoices: 0, totalExpenses: 0 };

        const startTime = new Date(shift.startTime);
        const endTime = shift.endTime ? new Date(shift.endTime) : new Date();

        // Get invoices during shift period
        const allInvoices = await storage.getAll('invoices');
        const shiftInvoices = allInvoices.filter(invoice => {
            const invoiceTime = new Date(invoice.createdAt);
            return invoiceTime >= startTime && invoiceTime <= endTime;
        });

        const totalSales = shiftInvoices.reduce((sum, invoice) => sum + invoice.finalAmount, 0);
        const totalInvoices = shiftInvoices.length;
        const totalExpenses = shift.expenses.reduce((sum, expense) => sum + expense.amount, 0);

        return { totalSales, totalInvoices, totalExpenses };
    }

    // Add expense to current shift
    async addExpense(description, amount, category = 'عام') {
        try {
            if (!this.currentShift) {
                throw new Error('لا يوجد شيفت مفتوح حالياً');
            }

            const expense = {
                id: storage.generateId(),
                description: description.trim(),
                amount: parseFloat(amount),
                category: category,
                timestamp: new Date().toISOString()
            };

            const expenses = [...this.currentShift.expenses, expense];
            const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0);

            const updatedShift = {
                ...this.currentShift,
                expenses: expenses,
                totalExpenses: totalExpenses,
                updatedAt: new Date().toISOString()
            };

            await storage.save('shifts', updatedShift);
            this.currentShift = updatedShift;

            // Update shifts array
            const shiftIndex = this.shifts.findIndex(s => s.id === this.currentShift.id);
            if (shiftIndex !== -1) {
                this.shifts[shiftIndex] = updatedShift;
            }

            // Force save to ensure persistence
            await this.saveCurrentShiftState();

            this.renderShifts();
            this.addActivity(`تم إضافة مصروف: ${description} - ${amount} جنيه`);

            return expense;
        } catch (error) {
            console.error('Error adding expense:', error);
            throw error;
        }
    }

    // Remove expense from current shift
    async removeExpense(expenseId) {
        try {
            if (!this.currentShift) {
                throw new Error('لا يوجد شيفت مفتوح حالياً');
            }

            const expenses = this.currentShift.expenses.filter(exp => exp.id !== expenseId);
            const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0);

            const updatedShift = {
                ...this.currentShift,
                expenses: expenses,
                totalExpenses: totalExpenses,
                updatedAt: new Date().toISOString()
            };

            await storage.save('shifts', updatedShift);
            this.currentShift = updatedShift;
            
            // Update shifts array
            const shiftIndex = this.shifts.findIndex(s => s.id === this.currentShift.id);
            if (shiftIndex !== -1) {
                this.shifts[shiftIndex] = updatedShift;
            }
            
            this.renderShifts();
            this.addActivity(`تم حذف مصروف`);
        } catch (error) {
            console.error('Error removing expense:', error);
            throw error;
        }
    }

    // Get shift by date range
    getShiftsByDateRange(startDate, endDate) {
        return this.shifts.filter(shift => {
            const shiftDate = new Date(shift.startTime);
            return shiftDate >= startDate && shiftDate <= endDate;
        });
    }

    // Get today's shifts
    getTodayShifts() {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        
        return this.getShiftsByDateRange(startOfDay, endOfDay);
    }

    // Render shifts
    renderShifts() {
        this.renderCurrentShiftInfo();
        this.renderShiftsHistory();
    }

    // Render current shift info
    renderCurrentShiftInfo() {
        const currentShiftInfo = document.getElementById('currentShiftInfo');
        if (!currentShiftInfo) return;

        if (!this.currentShift) {
            currentShiftInfo.innerHTML = `
                <div class="no-shift-card">
                    <div class="no-shift-content">
                        <i class="fas fa-clock fa-3x"></i>
                        <h3>لا يوجد شيفت مفتوح</h3>
                        <p>ابدأ شيفت جديد لبدء العمل</p>
                        <button class="btn btn-primary" onclick="shiftManager.showStartShiftModal()">
                            <i class="fas fa-play"></i> فتح شيفت جديد
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        const duration = this.calculateShiftDuration(this.currentShift.startTime);
        
        currentShiftInfo.innerHTML = `
            <div class="current-shift-card">
                <div class="shift-header">
                    <h3><i class="fas fa-clock"></i> الشيفت الحالي</h3>
                    <span class="shift-status active">نشط</span>
                </div>
                
                <div class="shift-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">كود الشيفت:</span>
                            <span class="info-value shift-code">${this.currentShift.shiftCode}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الموظف:</span>
                            <span class="info-value">${this.currentShift.employeeName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">وقت البداية:</span>
                            <span class="info-value">${this.formatDateTime(this.currentShift.startTime)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المدة:</span>
                            <span class="info-value">${duration}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رصيد البداية:</span>
                            <span class="info-value">${this.currentShift.openingCash.toFixed(2)} جنيه</span>
                        </div>
                    </div>
                </div>
                
                <div class="shift-stats" id="currentShiftStats">
                    <!-- سيتم تحديث الإحصائيات هنا -->
                </div>
                
                <div class="shift-actions">
                    <button class="btn btn-success" onclick="shiftManager.showAddExpenseModal()">
                        <i class="fas fa-plus"></i> إضافة مصروف
                    </button>
                    <button class="btn btn-warning" onclick="shiftManager.showEndShiftModal()">
                        <i class="fas fa-stop"></i> إغلاق الشيفت
                    </button>
                </div>
                
                ${this.currentShift.expenses.length > 0 ? `
                    <div class="shift-expenses">
                        <h4>المصاريف (${this.currentShift.expenses.length}):</h4>
                        <div class="expenses-list">
                            ${this.currentShift.expenses.map(expense => `
                                <div class="expense-item">
                                    <div class="expense-info">
                                        <span class="expense-desc">${expense.description}</span>
                                        <span class="expense-amount">${expense.amount.toFixed(2)} جنيه</span>
                                    </div>
                                    <button class="btn btn-sm btn-danger" onclick="shiftManager.removeExpense('${expense.id}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        <div class="expenses-total">
                            <strong>إجمالي المصاريف: ${this.currentShift.totalExpenses.toFixed(2)} جنيه</strong>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        // Update current shift stats
        this.updateCurrentShiftStats();
    }

    // Update current shift statistics
    async updateCurrentShiftStats() {
        if (!this.currentShift) return;

        try {
            const stats = await this.calculateShiftStats(this.currentShift.id);

            // Update shift object with latest stats
            this.currentShift.totalSales = stats.totalSales;
            this.currentShift.totalInvoices = stats.totalInvoices;
            this.currentShift.totalExpenses = stats.totalExpenses;
            this.currentShift.netAmount = stats.totalSales - stats.totalExpenses;
            this.currentShift.updatedAt = new Date().toISOString();

            // Save updated shift
            await storage.save('shifts', this.currentShift);

            // Update in shifts array
            const shiftIndex = this.shifts.findIndex(s => s.id === this.currentShift.id);
            if (shiftIndex !== -1) {
                this.shifts[shiftIndex] = this.currentShift;
            }

            // Update UI
            const currentShiftStats = document.getElementById('currentShiftStats');
            if (currentShiftStats) {
                currentShiftStats.innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">عدد الفواتير:</span>
                            <span class="stat-value">${stats.totalInvoices}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المبيعات:</span>
                            <span class="stat-value">${stats.totalSales.toFixed(2)} جنيه</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">المصاريف:</span>
                            <span class="stat-value">${stats.totalExpenses.toFixed(2)} جنيه</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">صافي المبيعات:</span>
                            <span class="stat-value">${(stats.totalSales - stats.totalExpenses).toFixed(2)} جنيه</span>
                        </div>
                    </div>
                `;
            }

            // Force save to backup
            await this.saveCurrentShiftState();

        } catch (error) {
            console.error('Error updating shift stats:', error);
        }
    }

    // Render shifts history
    renderShiftsHistory() {
        const shiftsHistory = document.getElementById('shiftsHistory');
        if (!shiftsHistory) return;

        const completedShifts = this.shifts.filter(shift => shift.status === 'completed');
        
        if (completedShifts.length === 0) {
            shiftsHistory.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-history fa-3x"></i>
                    <h3>لا يوجد تاريخ شيفتات</h3>
                    <p>ستظهر الشيفتات المكتملة هنا</p>
                </div>
            `;
            return;
        }

        shiftsHistory.innerHTML = `
            <div class="shifts-history-header">
                <h3>تاريخ الشيفتات</h3>
                <div class="history-stats">
                    <span>إجمالي الشيفتات: ${completedShifts.length}</span>
                </div>
            </div>
            <div class="shifts-list">
                ${completedShifts.map(shift => this.createShiftCard(shift)).join('')}
            </div>
        `;
    }

    // Create shift card HTML
    createShiftCard(shift) {
        const duration = this.calculateShiftDuration(shift.startTime, shift.endTime);
        const profit = shift.totalSales - shift.totalExpenses;
        
        return `
            <div class="shift-card fade-in">
                <div class="card-header">
                    <div class="shift-title">
                        <strong>${shift.shiftCode || 'بدون كود'}</strong>
                        <span class="shift-employee">${shift.employeeName}</span>
                        <span class="shift-date">${this.formatDate(shift.startTime)}</span>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="shiftManager.viewShiftDetails('${shift.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="shiftManager.printShiftReport('${shift.id}')" title="طباعة التقرير">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
                
                <div class="shift-summary">
                    <div class="summary-row">
                        <span>المدة:</span>
                        <span>${duration}</span>
                    </div>
                    <div class="summary-row">
                        <span>عدد الفواتير:</span>
                        <span>${shift.totalInvoices}</span>
                    </div>
                    <div class="summary-row">
                        <span>إجمالي المبيعات:</span>
                        <span>${shift.totalSales.toFixed(2)} جنيه</span>
                    </div>
                    <div class="summary-row">
                        <span>المصاريف:</span>
                        <span>${shift.totalExpenses.toFixed(2)} جنيه</span>
                    </div>
                    <div class="summary-row total-row">
                        <span><strong>صافي الربح:</strong></span>
                        <span class="${profit >= 0 ? 'profit' : 'loss'}">
                            <strong>${profit.toFixed(2)} جنيه</strong>
                        </span>
                    </div>
                </div>
            </div>
        `;
    }

    // Calculate shift duration
    calculateShiftDuration(startTime, endTime = null) {
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : new Date();
        const durationMs = end - start;
        
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        
        return `${hours}:${minutes.toString().padStart(2, '0')} ساعة`;
    }

    // Start auto-save system
    startAutoSave() {
        // Save current shift every 30 seconds
        setInterval(async () => {
            if (this.currentShift) {
                await this.saveCurrentShiftState();
            }
        }, 30000); // Save every 30 seconds

        // Update stats every minute
        setInterval(() => {
            if (this.currentShift) {
                this.updateCurrentShiftStats();
            }
        }, 60000); // Update every minute
    }

    // Setup event listeners
    setupEventListeners() {
        // Save on page unload
        window.addEventListener('beforeunload', async () => {
            if (this.currentShift) {
                await this.saveCurrentShiftState();
            }
        });

        // Save on visibility change (when user switches tabs)
        document.addEventListener('visibilitychange', async () => {
            if (document.hidden && this.currentShift) {
                await this.saveCurrentShiftState();
            }
        });
    }

    // Update shift status in dashboard
    updateShiftStatus() {
        const currentShiftElement = document.getElementById('currentShift');
        if (currentShiftElement) {
            currentShiftElement.textContent = this.currentShift ? 'مفتوح' : 'مغلق';
        }
    }

    // Show start shift modal
    showStartShiftModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>فتح شيفت جديد</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="startShiftForm">
                        <div class="form-group">
                            <label>اختر الموظف:</label>
                            <select id="employeeName" required>
                                <option value="المدير">المدير</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>رصيد البداية (جنيه):</label>
                            <input type="number" id="openingCash" value="0" step="0.01" min="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="shiftManager.confirmStartShift()">فتح الشيفت</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalOverlay').innerHTML = modalHTML;

        // Update employee options if employee manager is available
        this.updateEmployeeOptions();

        showModal();
    }

    // Update employee options in shift modal
    updateEmployeeOptions() {
        const employeeSelect = document.getElementById('employeeName');
        if (!employeeSelect || !window.employeeManager) return;

        const activeEmployees = window.employeeManager.getActiveEmployees();
        let options = '<option value="المدير">المدير</option>';

        activeEmployees.forEach(emp => {
            if (emp.permissions.canOpenShift) {
                options += `<option value="${emp.name}" data-code="${emp.employeeCode}">${emp.name} (${emp.employeeCode})</option>`;
            }
        });

        employeeSelect.innerHTML = options;
    }

    // Show end shift modal
    showEndShiftModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>إغلاق الشيفت</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="endShiftForm">
                        <div class="form-group">
                            <label>رصيد النهاية (جنيه):</label>
                            <input type="number" id="closingCash" value="0" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>ملاحظات:</label>
                            <textarea id="shiftNotes" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-warning" onclick="shiftManager.confirmEndShift()">إغلاق الشيفت</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Show add expense modal
    showAddExpenseModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>إضافة مصروف</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addExpenseForm">
                        <div class="form-group">
                            <label>وصف المصروف:</label>
                            <input type="text" id="expenseDescription" required>
                        </div>
                        <div class="form-group">
                            <label>المبلغ (جنيه):</label>
                            <input type="number" id="expenseAmount" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label>الفئة:</label>
                            <select id="expenseCategory">
                                <option value="عام">عام</option>
                                <option value="مشتريات">مشتريات</option>
                                <option value="صيانة">صيانة</option>
                                <option value="رواتب">رواتب</option>
                                <option value="فواتير">فواتير</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-success" onclick="shiftManager.confirmAddExpense()">إضافة</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Confirm start shift
    async confirmStartShift() {
        try {
            const employeeName = document.getElementById('employeeName').value;
            const openingCash = document.getElementById('openingCash').value;
            
            if (!employeeName.trim()) {
                alert('يرجى إدخال اسم الموظف');
                return;
            }
            
            await this.startShift(employeeName, openingCash);
            closeModal();
        } catch (error) {
            alert(error.message);
        }
    }

    // Confirm end shift
    async confirmEndShift() {
        try {
            const closingCash = document.getElementById('closingCash').value;
            const notes = document.getElementById('shiftNotes').value;
            
            await this.endShift(closingCash, notes);
            closeModal();
        } catch (error) {
            alert(error.message);
        }
    }

    // Confirm add expense
    async confirmAddExpense() {
        try {
            const description = document.getElementById('expenseDescription').value;
            const amount = document.getElementById('expenseAmount').value;
            const category = document.getElementById('expenseCategory').value;
            
            if (!description.trim() || !amount) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            await this.addExpense(description, amount, category);
            closeModal();
        } catch (error) {
            alert(error.message);
        }
    }

    // View shift details
    viewShiftDetails(shiftId) {
        const shift = this.shifts.find(s => s.id === shiftId);
        if (!shift) return;

        const duration = this.calculateShiftDuration(shift.startTime, shift.endTime);
        const profit = shift.totalSales - shift.totalExpenses;

        const modalHTML = `
            <div class="modal large-modal">
                <div class="modal-header">
                    <h3>تفاصيل الشيفت - ${shift.employeeName}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="shift-details-view">
                        <div class="shift-basic-info">
                            <h4>البيانات الأساسية</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">كود الشيفت:</span>
                                    <span class="info-value shift-code">${shift.shiftCode || 'بدون كود'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">الموظف:</span>
                                    <span class="info-value">${shift.employeeName}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">تاريخ الشيفت:</span>
                                    <span class="info-value">${this.formatDate(shift.startTime)}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">وقت البداية:</span>
                                    <span class="info-value">${this.formatDateTime(shift.startTime)}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">وقت النهاية:</span>
                                    <span class="info-value">${shift.endTime ? this.formatDateTime(shift.endTime) : 'لم ينته بعد'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">مدة الشيفت:</span>
                                    <span class="info-value">${duration}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">الحالة:</span>
                                    <span class="info-value">${shift.status === 'active' ? 'نشط' : 'مكتمل'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="shift-financial-info">
                            <h4>البيانات المالية</h4>
                            <div class="financial-grid">
                                <div class="financial-item">
                                    <span class="financial-label">رصيد البداية:</span>
                                    <span class="financial-value">${shift.openingCash.toFixed(2)} جنيه</span>
                                </div>
                                <div class="financial-item">
                                    <span class="financial-label">رصيد النهاية:</span>
                                    <span class="financial-value">${shift.closingCash.toFixed(2)} جنيه</span>
                                </div>
                                <div class="financial-item">
                                    <span class="financial-label">عدد الفواتير:</span>
                                    <span class="financial-value">${shift.totalInvoices}</span>
                                </div>
                                <div class="financial-item">
                                    <span class="financial-label">إجمالي المبيعات:</span>
                                    <span class="financial-value revenue">${shift.totalSales.toFixed(2)} جنيه</span>
                                </div>
                                <div class="financial-item">
                                    <span class="financial-label">إجمالي المصاريف:</span>
                                    <span class="financial-value expense">${shift.totalExpenses.toFixed(2)} جنيه</span>
                                </div>
                                <div class="financial-item total">
                                    <span class="financial-label"><strong>صافي الربح:</strong></span>
                                    <span class="financial-value ${profit >= 0 ? 'profit' : 'loss'}">
                                        <strong>${profit.toFixed(2)} جنيه</strong>
                                    </span>
                                </div>
                            </div>
                        </div>

                        ${shift.expenses.length > 0 ? `
                            <div class="shift-expenses-details">
                                <h4>تفاصيل المصاريف (${shift.expenses.length})</h4>
                                <table class="expenses-table">
                                    <thead>
                                        <tr>
                                            <th>الوصف</th>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${shift.expenses.map(expense => `
                                            <tr>
                                                <td>${expense.description}</td>
                                                <td>${expense.category}</td>
                                                <td>${expense.amount.toFixed(2)} جنيه</td>
                                                <td>${this.formatDateTime(expense.timestamp)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : '<p>لا توجد مصاريف في هذا الشيفت</p>'}

                        ${shift.notes ? `
                            <div class="shift-notes">
                                <h4>ملاحظات</h4>
                                <p>${shift.notes}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="shiftManager.printShiftReport('${shiftId}')">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Print shift report
    printShiftReport(shiftId) {
        const shift = this.shifts.find(s => s.id === shiftId);
        if (!shift) return;

        const duration = this.calculateShiftDuration(shift.startTime, shift.endTime);
        const profit = shift.totalSales - shift.totalExpenses;
        const cafeName = 'مقهى الأصدقاء'; // Get from settings

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الشيفت - ${shift.employeeName}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .report-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 10px; }
                    .report-section { margin-bottom: 20px; }
                    .report-section h3 { color: #2c3e50; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
                    .info-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
                    .info-table td { padding: 8px; border-bottom: 1px solid #eee; }
                    .info-table td:first-child { font-weight: bold; width: 30%; }
                    .expenses-table { width: 100%; border-collapse: collapse; }
                    .expenses-table th, .expenses-table td { border: 1px solid #000; padding: 8px; text-align: right; }
                    .expenses-table th { background-color: #f0f0f0; }
                    .total-row { font-weight: bold; font-size: 1.2em; background-color: #f9f9f9; }
                    .profit { color: green; }
                    .loss { color: red; }
                    .footer { text-align: center; margin-top: 30px; font-size: 0.9em; color: #666; }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>${cafeName}</h1>
                    <h2>تقرير الشيفت</h2>
                    <p><strong>كود الشيفت:</strong> ${shift.shiftCode || 'بدون كود'}</p>
                    <p>الموظف: ${shift.employeeName} | التاريخ: ${this.formatDate(shift.startTime)}</p>
                </div>

                <div class="report-section">
                    <h3>بيانات الشيفت</h3>
                    <table class="info-table">
                        <tr><td>وقت البداية</td><td>${this.formatDateTime(shift.startTime)}</td></tr>
                        <tr><td>وقت النهاية</td><td>${shift.endTime ? this.formatDateTime(shift.endTime) : 'لم ينته بعد'}</td></tr>
                        <tr><td>مدة الشيفت</td><td>${duration}</td></tr>
                        <tr><td>رصيد البداية</td><td>${shift.openingCash.toFixed(2)} جنيه</td></tr>
                        <tr><td>رصيد النهاية</td><td>${shift.closingCash.toFixed(2)} جنيه</td></tr>
                    </table>
                </div>

                <div class="report-section">
                    <h3>الملخص المالي</h3>
                    <table class="info-table">
                        <tr><td>عدد الفواتير</td><td>${shift.totalInvoices}</td></tr>
                        <tr><td>إجمالي المبيعات</td><td>${shift.totalSales.toFixed(2)} جنيه</td></tr>
                        <tr><td>إجمالي المصاريف</td><td>${shift.totalExpenses.toFixed(2)} جنيه</td></tr>
                        <tr class="total-row"><td>صافي الربح</td><td class="${profit >= 0 ? 'profit' : 'loss'}">${profit.toFixed(2)} جنيه</td></tr>
                    </table>
                </div>

                ${shift.expenses.length > 0 ? `
                    <div class="report-section">
                        <h3>تفاصيل المصاريف</h3>
                        <table class="expenses-table">
                            <thead>
                                <tr>
                                    <th>الوصف</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>الوقت</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${shift.expenses.map(expense => `
                                    <tr>
                                        <td>${expense.description}</td>
                                        <td>${expense.category}</td>
                                        <td>${expense.amount.toFixed(2)} جنيه</td>
                                        <td>${this.formatDateTime(expense.timestamp)}</td>
                                    </tr>
                                `).join('')}
                                <tr class="total-row">
                                    <td colspan="2">الإجمالي</td>
                                    <td>${shift.totalExpenses.toFixed(2)} جنيه</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                ` : ''}

                ${shift.notes ? `
                    <div class="report-section">
                        <h3>ملاحظات</h3>
                        <p>${shift.notes}</p>
                    </div>
                ` : ''}

                <div class="footer">
                    <p>تم طباعة التقرير في: ${new Date().toLocaleString('ar-EG')}</p>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG');
    }

    // Format date only
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }

    // Add activity to recent activity list
    addActivity(message) {
        if (window.activityManager) {
            window.activityManager.addActivity(message);
        }
    }
}

// Initialize shift manager
const shiftManager = new ShiftManager();
