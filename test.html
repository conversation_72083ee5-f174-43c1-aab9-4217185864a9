<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة المقهى</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #ecf0f1;
        }
        .success {
            background: #d5f4e6;
            color: #27ae60;
        }
        .error {
            background: #fadbd8;
            color: #e74c3c;
        }
        .info {
            background: #d6eaf8;
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام إدارة المقهى</h1>
        <p>هذه الصفحة لاختبار وظائف النظام قبل الاستخدام الفعلي.</p>

        <div class="test-section">
            <h3>🔧 اختبار التخزين المحلي</h3>
            <button class="test-button" onclick="testStorage()">اختبار التخزين</button>
            <button class="test-button" onclick="clearStorage()">مسح البيانات</button>
            <div id="storageResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>👥 اختبار إدارة العملاء</h3>
            <button class="test-button" onclick="testCustomers()">إضافة عملاء تجريبيين</button>
            <button class="test-button" onclick="testActiveCustomer()">محاكاة عميل نشط</button>
            <div id="customersResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>☕ اختبار إدارة المنتجات</h3>
            <button class="test-button" onclick="testProducts()">إضافة منتجات تجريبية</button>
            <button class="test-button" onclick="testProductSearch()">اختبار البحث</button>
            <div id="productsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🧾 اختبار الفواتير</h3>
            <button class="test-button" onclick="testInvoices()">إنشاء فواتير تجريبية</button>
            <button class="test-button" onclick="testInvoicePrint()">اختبار الطباعة</button>
            <div id="invoicesResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>⏰ اختبار الشيفتات</h3>
            <button class="test-button" onclick="testShifts()">محاكاة شيفت</button>
            <button class="test-button" onclick="testExpenses()">إضافة مصاريف</button>
            <button class="test-button" onclick="testShiftPersistence()">اختبار حفظ الشيفت</button>
            <div id="shiftsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 اختبار التقارير</h3>
            <button class="test-button" onclick="testReports()">إنشاء تقارير</button>
            <button class="test-button" onclick="testDataExport()">اختبار التصدير</button>
            <div id="reportsResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔒 اختبار الإغلاق الآمن</h3>
            <button class="test-button" onclick="testSafeExit()">اختبار الإغلاق الآمن</button>
            <button class="test-button" onclick="testAutoSave()">اختبار الحفظ التلقائي</button>
            <div id="safeExitResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 تشغيل النظام</h3>
            <p>بعد اكتمال الاختبارات بنجاح، يمكنك تشغيل النظام:</p>
            <button class="test-button" onclick="openMainApp()" style="background: #27ae60;">
                🎯 فتح نظام إدارة مكان المذاكرة
            </button>
        </div>
    </div>

    <script>
        // Test Storage
        function testStorage() {
            const result = document.getElementById('storageResult');
            try {
                // Test localStorage
                localStorage.setItem('test', 'value');
                const testValue = localStorage.getItem('test');
                localStorage.removeItem('test');

                // Test IndexedDB
                if ('indexedDB' in window) {
                    result.innerHTML = `
                        <div class="success">
                            ✅ التخزين المحلي يعمل بشكل صحيح<br>
                            📦 LocalStorage: متاح<br>
                            🗄️ IndexedDB: متاح
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="info">
                            ⚠️ IndexedDB غير متاح، سيتم استخدام LocalStorage
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        ❌ خطأ في التخزين المحلي: ${error.message}
                    </div>
                `;
            }
        }

        function clearStorage() {
            localStorage.clear();
            if ('indexedDB' in window) {
                indexedDB.deleteDatabase('CafeManagementDB');
            }
            document.getElementById('storageResult').innerHTML = `
                <div class="success">🗑️ تم مسح جميع البيانات</div>
            `;
        }

        // Test Customers
        function testCustomers() {
            const result = document.getElementById('customersResult');
            const testCustomers = [
                { name: 'أحمد محمد', phone: '01234567890', notes: 'عميل دائم' },
                { name: 'فاطمة علي', phone: '01987654321', notes: 'يفضل الشاي' },
                { name: 'محمود حسن', phone: '01122334455', notes: 'يأتي مساءً' }
            ];

            try {
                testCustomers.forEach(customer => {
                    const id = Date.now() + Math.random();
                    const customerData = {
                        ...customer,
                        id: id,
                        status: 'completed',
                        entryTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
                        exitTime: new Date().toISOString(),
                        totalTime: Math.floor(Math.random() * 180) + 30,
                        totalCost: Math.floor(Math.random() * 100) + 20,
                        purchases: [],
                        createdAt: new Date().toISOString()
                    };
                    localStorage.setItem(`customer_${id}`, JSON.stringify(customerData));
                });

                result.innerHTML = `
                    <div class="success">
                        ✅ تم إضافة ${testCustomers.length} عملاء تجريبيين بنجاح
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في إضافة العملاء: ${error.message}</div>
                `;
            }
        }

        function testActiveCustomer() {
            const result = document.getElementById('customersResult');
            try {
                const id = Date.now();
                const activeCustomer = {
                    id: id,
                    name: 'عميل نشط تجريبي',
                    phone: '01555666777',
                    status: 'active',
                    entryTime: new Date().toISOString(),
                    exitTime: null,
                    totalTime: 0,
                    totalCost: 0,
                    purchases: [],
                    createdAt: new Date().toISOString()
                };
                localStorage.setItem(`customer_${id}`, JSON.stringify(activeCustomer));

                result.innerHTML = `
                    <div class="success">✅ تم إنشاء عميل نشط تجريبي</div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في إنشاء العميل النشط: ${error.message}</div>
                `;
            }
        }

        // Test Products
        function testProducts() {
            const result = document.getElementById('productsResult');
            const testProducts = [
                { name: 'شاي', code: 'TEA001', price: 5, category: 'مشروبات' },
                { name: 'قهوة', code: 'COFFEE001', price: 8, category: 'مشروبات' },
                { name: 'عصير برتقال', code: 'JUICE001', price: 12, category: 'مشروبات' },
                { name: 'كيك شوكولاتة', code: 'CAKE001', price: 20, category: 'حلويات' },
                { name: 'سندوتش جبنة', code: 'SAND001', price: 15, category: 'مأكولات' }
            ];

            try {
                testProducts.forEach(product => {
                    const id = Date.now() + Math.random();
                    const productData = {
                        ...product,
                        id: id,
                        isActive: true,
                        createdAt: new Date().toISOString()
                    };
                    localStorage.setItem(`product_${id}`, JSON.stringify(productData));
                });

                result.innerHTML = `
                    <div class="success">
                        ✅ تم إضافة ${testProducts.length} منتجات تجريبية بنجاح
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في إضافة المنتجات: ${error.message}</div>
                `;
            }
        }

        function testProductSearch() {
            const result = document.getElementById('productsResult');
            result.innerHTML = `
                <div class="info">
                    🔍 اختبار البحث:<br>
                    - البحث بالاسم: "شاي"<br>
                    - البحث بالكود: "TEA001"<br>
                    - البحث بالفئة: "مشروبات"
                </div>
            `;
        }

        // Test Invoices
        function testInvoices() {
            const result = document.getElementById('invoicesResult');
            try {
                const invoiceId = Date.now();
                const testInvoice = {
                    id: invoiceId,
                    invoiceNumber: `INV-${new Date().getFullYear()}-001`,
                    customerName: 'عميل تجريبي',
                    customerPhone: '01234567890',
                    entryTime: new Date(Date.now() - 7200000).toISOString(),
                    exitTime: new Date().toISOString(),
                    totalTime: 120,
                    purchases: [
                        { productName: 'شاي', quantity: 2, price: 5, total: 10 },
                        { productName: 'كيك', quantity: 1, price: 20, total: 20 }
                    ],
                    timeCost: 20,
                    purchasesCost: 30,
                    totalCost: 50,
                    finalAmount: 50,
                    createdAt: new Date().toISOString()
                };
                localStorage.setItem(`invoice_${invoiceId}`, JSON.stringify(testInvoice));

                result.innerHTML = `
                    <div class="success">✅ تم إنشاء فاتورة تجريبية بنجاح</div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في إنشاء الفاتورة: ${error.message}</div>
                `;
            }
        }

        function testInvoicePrint() {
            const result = document.getElementById('invoicesResult');
            if (window.print) {
                result.innerHTML = `
                    <div class="success">✅ وظيفة الطباعة متاحة في المتصفح</div>
                `;
            } else {
                result.innerHTML = `
                    <div class="error">❌ وظيفة الطباعة غير متاحة</div>
                `;
            }
        }

        // Test Shifts
        function testShifts() {
            const result = document.getElementById('shiftsResult');
            try {
                const shiftId = Date.now();
                const today = new Date();
                const datePrefix = today.getFullYear().toString().substr(-2) +
                                  (today.getMonth() + 1).toString().padStart(2, '0') +
                                  today.getDate().toString().padStart(2, '0');
                const shiftCode = `SH-${datePrefix}-001`;

                const testShift = {
                    id: shiftId,
                    shiftCode: shiftCode,
                    employeeName: 'موظف تجريبي',
                    startTime: new Date(Date.now() - 28800000).toISOString(),
                    endTime: new Date().toISOString(),
                    openingCash: 100,
                    closingCash: 250,
                    totalSales: 200,
                    totalInvoices: 8,
                    expenses: [],
                    totalExpenses: 50,
                    netAmount: 150,
                    status: 'completed',
                    createdAt: new Date().toISOString()
                };
                localStorage.setItem(`shift_${shiftId}`, JSON.stringify(testShift));

                result.innerHTML = `
                    <div class="success">✅ تم إنشاء شيفت تجريبي بنجاح<br>كود الشيفت: ${shiftCode}</div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في إنشاء الشيفت: ${error.message}</div>
                `;
            }
        }

        function testShiftPersistence() {
            const result = document.getElementById('shiftsResult');
            try {
                // Test saving and retrieving shift data
                const testData = {
                    currentShift: { id: 'test123', shiftCode: 'SH-250628-001', status: 'active' },
                    shifts: [
                        { id: 'test123', shiftCode: 'SH-250628-001', status: 'active' },
                        { id: 'test124', shiftCode: 'SH-250628-002', status: 'completed' }
                    ]
                };

                // Save to localStorage
                localStorage.setItem('currentShiftBackup', JSON.stringify(testData.currentShift));
                localStorage.setItem('shiftsBackup', JSON.stringify(testData.shifts));

                // Try to retrieve
                const retrievedCurrent = JSON.parse(localStorage.getItem('currentShiftBackup'));
                const retrievedShifts = JSON.parse(localStorage.getItem('shiftsBackup'));

                if (retrievedCurrent && retrievedShifts && retrievedShifts.length === 2) {
                    result.innerHTML = `
                        <div class="success">
                            ✅ اختبار حفظ البيانات نجح<br>
                            - الشيفت الحالي: ${retrievedCurrent.shiftCode}<br>
                            - عدد الشيفتات المحفوظة: ${retrievedShifts.length}
                        </div>
                    `;
                } else {
                    throw new Error('فشل في استرجاع البيانات');
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في اختبار الحفظ: ${error.message}</div>
                `;
            }
        }

        function testExpenses() {
            const result = document.getElementById('shiftsResult');
            result.innerHTML = `
                <div class="info">
                    💰 اختبار المصاريف:<br>
                    - إضافة مصروف جديد<br>
                    - حساب إجمالي المصاريف<br>
                    - تأثير على صافي الربح
                </div>
            `;
        }

        // Test Reports
        function testReports() {
            const result = document.getElementById('reportsResult');
            result.innerHTML = `
                <div class="success">
                    📊 تم إنشاء التقارير التجريبية:<br>
                    - التقرير اليومي: ✅<br>
                    - التقرير الأسبوعي: ✅<br>
                    - التقرير الشهري: ✅
                </div>
            `;
        }

        function testDataExport() {
            const result = document.getElementById('reportsResult');
            try {
                const testData = {
                    customers: [],
                    products: [],
                    invoices: [],
                    shifts: [],
                    exportDate: new Date().toISOString()
                };
                const blob = new Blob([JSON.stringify(testData)], { type: 'application/json' });
                result.innerHTML = `
                    <div class="success">✅ وظيفة التصدير تعمل بشكل صحيح</div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ خطأ في التصدير: ${error.message}</div>
                `;
            }
        }

        // Open Main App
        function openMainApp() {
            window.open('index.html', '_blank');
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testStorage();
        };
    </script>
</body>
</html>
