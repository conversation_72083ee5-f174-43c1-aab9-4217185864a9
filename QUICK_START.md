# 🚀 دليل البدء السريع - نظام إدارة مكان المذاكرة

## ⚡ التشغيل في 3 خطوات

### للويندوز:
1. **انقر نقراً مزدوجاً على `start_server.bat`**
2. **انتظر فتح المتصفح تلقائياً**
3. **ابدأ الاستخدام!**

### للماك/لينكس:
1. **افتح Terminal في مجلد المشروع**
2. **نفذ: `./start_server.sh`**
3. **ابدأ الاستخدام!**

---

## 🎯 أول استخدام

### 1. إضافة طالب جديد:
- اذهب لقسم "العملاء"
- اضغط "إضافة عميل"
- أدخل الاسم والهاتف
- **التايمر سيبدأ تلقائياً!**

### 2. إضافة منتجات:
- اذهب لقسم "المنتجات"
- اضغط "إضافة منتج"
- أدخل التفاصيل والسعر

### 3. إنشاء فاتورة:
- من بطاقة الطالب النشط
- اضغط زر "إنشاء فاتورة" 🧾
- **الطالب يستمر في المذاكرة!**

### 4. فتح شيفت:
- اذهب لقسم "الشيفتات"
- اضغط "فتح شيفت جديد"
- أدخل اسم الموظف ورصيد البداية

---

## 🔥 الميزات الرئيسية

### ⏱️ تايمر مرئي:
- يعمل بالثواني
- تحديث فوري للتكلفة
- رسوم متحركة جذابة

### 🧾 فواتير ذكية:
- إنشاء فوري بدون إنهاء الجلسة
- طباعة احترافية
- حفظ تلقائي

### 💾 حفظ آمن:
- حفظ تلقائي كل 30 ثانية
- نسخ احتياطي متعددة
- استعادة تلقائية

### 📊 تقارير شاملة:
- يومية وأسبوعية وشهرية
- إحصائيات مفصلة
- قابلة للطباعة

---

## ❓ مشاكل شائعة وحلولها

### "Python غير مثبت":
- حمل من: https://www.python.org/downloads/
- تأكد من تحديد "Add Python to PATH"

### "لا يتم حفظ البيانات":
- استخدم السيرفر المحلي
- لا تفتح index.html مباشرة

### "المنفذ مستخدم":
- السيرفر سيجد منفذ متاح تلقائياً
- أو أعد تشغيل الكمبيوتر

---

## 🎨 نصائح للاستخدام الأمثل

### للطلاب:
- استخدم التايمر المرئي لتحفيز الطلاب
- أنشئ فواتير جزئية للمراجعة
- اعرض الوقت والتكلفة بوضوح

### للإدارة:
- راجع التقارير اليومية
- استخدم نظام الشيفتات لتتبع الموظفين
- احتفظ بنسخ احتياطية منتظمة

### للمحاسبة:
- استخدم نظام الفواتير المتقدم
- تتبع المصاريف في كل شيفت
- اطبع التقارير المالية

---

## 🔗 روابط مفيدة

- **العرض التوضيحي:** افتح `demo.html`
- **اختبار النظام:** افتح `test.html`
- **الدليل الكامل:** اقرأ `README.md`

---

## 📞 الدعم

إذا واجهت أي مشكلة:
1. راجع قسم "حل المشاكل" في README.md
2. تأكد من تثبيت Python بشكل صحيح
3. جرب إعادة تشغيل السيرفر

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة مكان المذاكرة**
