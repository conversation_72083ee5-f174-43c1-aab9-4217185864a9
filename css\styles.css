/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme Colors */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    
    --bg-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
    
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #34495e;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    
    --bg-color: #1a1a1a;
    --surface-color: #2d2d2d;
    --text-color: #ffffff;
    --text-muted: #adb5bd;
    --border-color: #495057;
    
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.4);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: var(--surface-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Navigation */
.nav {
    background: var(--primary-color);
    box-shadow: var(--shadow);
}

.nav-list {
    display: flex;
    list-style: none;
    overflow-x: auto;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: var(--transition);
    white-space: nowrap;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    background: var(--secondary-color);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-primary { background: var(--secondary-color); }
.btn-success { background: var(--success-color); }
.btn-warning { background: var(--warning-color); }
.btn-danger { background: var(--danger-color); }
.btn-info { background: var(--info-color); }
.btn-secondary { background: var(--text-muted); }

.btn-icon {
    padding: 0.5rem;
    border-radius: 50%;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 140px);
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--secondary-color);
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-content p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.quick-actions h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Search Bar */
.search-bar {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--surface-color);
    color: var(--text-color);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--secondary-color);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* Grids */
.customers-grid,
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.customer-card,
.product-card {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.customer-card:hover,
.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

/* Active Customer Styling */
.customer-card.active-customer {
    border: 2px solid var(--success-color);
    box-shadow: 0 0 20px rgba(46, 204, 113, 0.3);
}

/* Timer Display */
.timer-display {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin: 1rem 0;
    border-radius: var(--border-radius);
}

.timer-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
    background-size: 80px 80px;
    opacity: 0.3;
}

.timer-circle {
    position: relative;
    z-index: 2;
    margin-bottom: 1rem;
}

.timer-time {
    font-size: 2.5rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 0.5rem;
    letter-spacing: 2px;
}

.timer-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.timer-cost {
    position: relative;
    z-index: 2;
    border-top: 1px solid rgba(255,255,255,0.3);
    padding-top: 1rem;
}

.cost-amount {
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Animated timer for active customers */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.active-customer .timer-time {
    animation: pulse 2s ease-in-out infinite;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-info {
    margin-bottom: 1rem;
}

.card-info p {
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.card-info strong {
    color: var(--text-color);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: var(--success-color);
    color: white;
}

.status-inactive {
    background: var(--text-muted);
    color: white;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--surface-color);
    color: var(--text-color);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-list {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .customers-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .modal {
        width: 95%;
        margin: 1rem;
    }
}

/* Filter Bar */
.filter-bar {
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-bar select {
    padding: 0.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    color: var(--text-color);
    transition: var(--transition);
}

.filter-bar select:focus {
    outline: none;
    border-color: var(--secondary-color);
}

/* Product Options */
.product-option {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    background: var(--surface-color);
}

.product-option:hover {
    border-color: var(--secondary-color);
    background: var(--bg-color);
    transform: translateY(-2px);
}

.product-info h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.product-info p {
    margin: 0.25rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.product-price {
    font-weight: bold;
    color: var(--success-color) !important;
    font-size: 1rem !important;
}

/* Customer Details View */
.customer-details-view {
    max-height: 70vh;
    overflow-y: auto;
}

.customer-details-view > div {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: var(--border-radius);
}

.customer-details-view h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.customer-details-view p {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

/* Products Selection */
.products-selection {
    max-height: 60vh;
    overflow-y: auto;
}

.products-selection .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--text-muted);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.error-message {
    background: var(--danger-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
}

.success-message {
    background: var(--success-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
}

/* Notification Toast */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    padding: 1rem;
    z-index: 3000;
    max-width: 300px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline-offset: 0;
}

/* Print Specific */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.hidden { display: none !important; }
.visible { display: block !important; }

.pointer { cursor: pointer; }
.no-select { user-select: none; }

.full-width { width: 100%; }
.full-height { height: 100%; }

/* Additional Report Styles */
.customer-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: var(--surface-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-card h5 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin: 0;
}

.hourly-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.hour-stat {
    background: var(--surface-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-color);
}

.hour-label {
    display: block;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.hour-invoices,
.hour-revenue {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.hour-revenue {
    color: var(--success-color);
    font-weight: 500;
}

/* Shift Details Styles */
.shift-details-view {
    max-height: 70vh;
    overflow-y: auto;
}

.shift-basic-info,
.shift-financial-info,
.shift-expenses-details,
.shift-notes {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: var(--border-radius);
}

.shift-basic-info h4,
.shift-financial-info h4,
.shift-expenses-details h4,
.shift-notes h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
}

.financial-item.total {
    border-left-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.financial-item.total .financial-label,
.financial-item.total .financial-value {
    color: white;
}

.financial-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.financial-value {
    font-weight: bold;
    color: var(--text-color);
}

.financial-value.revenue {
    color: var(--success-color);
}

.financial-value.expense {
    color: var(--danger-color);
}

.expenses-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.expenses-table th,
.expenses-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.expenses-table th {
    background: var(--surface-color);
    font-weight: bold;
    color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
}

.expenses-table tbody tr:hover {
    background: var(--bg-color);
}

/* Enhanced Modal Styles */
.modal.large-modal {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
}

.modal-body {
    max-height: 60vh;
    overflow-y: auto;
    padding: 1.5rem;
}

/* Enhanced Button Styles */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-icon-only {
    padding: 0.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
}

.btn-group .btn {
    flex: 1;
}

/* Enhanced Card Styles */
.card-hover {
    transition: var(--transition);
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.card-compact {
    padding: 1rem;
}

.card-expanded {
    padding: 2rem;
}

/* Progress Indicators */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--secondary-color);
    transition: width 0.3s ease;
}

.progress-fill.success {
    background: var(--success-color);
}

.progress-fill.warning {
    background: var(--warning-color);
}

.progress-fill.danger {
    background: var(--danger-color);
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 12px;
    text-transform: uppercase;
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-secondary {
    background: var(--text-muted);
    color: white;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--border-color) 25%, var(--bg-color) 50%, var(--border-color) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    width: 60%;
}

.skeleton-card {
    height: 200px;
    border-radius: var(--border-radius);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .nav-list {
        flex-wrap: wrap;
        justify-content: center;
    }

    .modal.large-modal {
        width: 98%;
        margin: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 10px;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .btn-group {
        flex-direction: column;
    }

    .financial-grid,
    .info-grid {
        grid-template-columns: 1fr;
    }

    .hourly-stats {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .customer-stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Advanced Features Styles */

/* Global Search */
.global-search-container {
    padding: 1rem 0;
}

.search-results {
    max-height: 60vh;
    overflow-y: auto;
    margin-top: 1rem;
}

.search-hint {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

.no-results {
    text-align: center;
    color: var(--text-muted);
    padding: 2rem;
}

.result-group {
    margin-bottom: 2rem;
}

.result-group h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.result-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.result-item {
    padding: 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.result-item:hover {
    background: var(--bg-color);
    border-color: var(--secondary-color);
    transform: translateX(5px);
}

.result-title {
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.result-details {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
}

.notification {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    opacity: 0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification.success .notification-content i {
    color: var(--success-color);
}

.notification.error .notification-content i {
    color: var(--danger-color);
}

.notification.warning .notification-content i {
    color: var(--warning-color);
}

.notification.info .notification-content i {
    color: var(--info-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.notification-close:hover {
    background: var(--border-color);
    color: var(--text-color);
}

/* Permission-based visibility */
[data-permission] {
    transition: opacity 0.3s ease;
}

[data-permission].hidden {
    opacity: 0.5;
    pointer-events: none;
}

/* Enhanced Search Bar */
.search-bar.enhanced {
    position: relative;
    display: flex;
    align-items: center;
}

.search-bar.enhanced .search-input {
    padding-right: 3rem;
}

.search-bar.enhanced .search-actions {
    position: absolute;
    right: 0.5rem;
    display: flex;
    gap: 0.25rem;
}

.search-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
}

.search-action-btn:hover {
    background: var(--border-color);
    color: var(--text-color);
}

/* Data Export/Import Styles */
.export-import-section {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.export-import-section h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.export-import-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.file-input-wrapper input[type=file] {
    position: absolute;
    left: -9999px;
}

.file-input-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.file-input-label:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* Performance Indicators */
.performance-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.performance-indicator.show {
    opacity: 1;
}

/* Role Badge */
.role-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-right: 0.5rem;
}

.role-badge.admin {
    background: var(--danger-color);
}

.role-badge.cashier {
    background: var(--warning-color);
}

.role-badge.viewer {
    background: var(--info-color);
}

/* Advanced Table Styles */
.advanced-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.advanced-table th {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.advanced-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.advanced-table tbody tr:hover {
    background: var(--bg-color);
}

.advanced-table tbody tr:last-child td {
    border-bottom: none;
}

/* Sortable Table Headers */
.sortable-header {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable-header:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sortable-header::after {
    content: '';
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid currentColor;
    opacity: 0.5;
}

.sortable-header.asc::after {
    border-bottom: none;
    border-top: 4px solid currentColor;
}

.sortable-header.desc::after {
    border-bottom: 4px solid currentColor;
    border-top: none;
}

/* Quick Actions Panel */
.quick-actions-panel {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.quick-actions-panel.show {
    opacity: 1;
    visibility: visible;
}

.quick-action-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: var(--secondary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition);
    position: relative;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.quick-action-btn::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
}

.quick-action-btn:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive for Advanced Features */
@media (max-width: 768px) {
    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification {
        margin: 0;
    }

    .quick-actions-panel {
        right: 10px;
        bottom: 20px;
        top: auto;
        transform: none;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .export-import-actions {
        flex-direction: column;
    }

    .result-item:hover {
        transform: none;
    }

    .performance-indicator {
        left: 10px;
        right: 10px;
        text-align: center;
    }
}

/* Recent Activity */
.recent-activity {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-message {
    color: var(--text-color);
    font-size: 0.9rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.8rem;
    white-space: nowrap;
}

.no-activity {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-muted);
}

.empty-state i {
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* Category Sections */
.category-section {
    margin-bottom: 2rem;
}

.category-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
    color: var(--primary-color);
}

.products-category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.product-inactive {
    opacity: 0.6;
}

.product-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Customer Selection */
.customer-selection {
    display: grid;
    gap: 1rem;
}

.customer-option {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.customer-option:hover {
    border-color: var(--secondary-color);
    background: var(--bg-color);
}

.customer-option h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.customer-option p {
    margin: 0.25rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Invoices */
.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.invoices-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.invoices-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.filter-input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    color: var(--text-color);
}

.invoices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.invoice-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.invoice-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.invoice-number {
    font-size: 1.1rem;
    color: var(--primary-color);
}

.invoice-info {
    padding: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.info-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-color);
    font-weight: 500;
}

.total-row {
    border-top: 2px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: bold;
}

.total-row .info-value {
    color: var(--success-color);
    font-size: 1.1rem;
}

.discount {
    color: var(--warning-color) !important;
}

.invoice-purchases {
    padding: 1rem;
    background: var(--bg-color);
    border-top: 1px solid var(--border-color);
}

.invoice-purchases h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.purchases-list {
    max-height: 150px;
    overflow-y: auto;
}

.purchase-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Shifts */
.no-shift-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    text-align: center;
}

.no-shift-content i {
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.no-shift-content h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.no-shift-content p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.current-shift-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.shift-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--primary-color);
    color: white;
}

.shift-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.shift-status.active {
    background: var(--success-color);
}

.shift-info {
    padding: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item .info-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.info-item .info-value {
    font-weight: 500;
    color: var(--text-color);
}

.shift-stats {
    padding: 0 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.shift-actions {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
}

.shift-expenses {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-color);
}

.shift-expenses h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.expenses-list {
    margin-bottom: 1rem;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.expense-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.expense-desc {
    font-weight: 500;
    color: var(--text-color);
}

.expense-amount {
    color: var(--danger-color);
    font-size: 0.9rem;
}

.expenses-total {
    text-align: right;
    padding: 1rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    color: var(--danger-color);
}

.shifts-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.history-stats {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.shifts-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.shift-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.shift-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.shift-title {
    display: flex;
    flex-direction: column;
}

.shift-date {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: normal;
}

.shift-summary {
    padding: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.summary-row.total-row {
    border-top: 2px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.profit {
    color: var(--success-color) !important;
}

.loss {
    color: var(--danger-color) !important;
}

/* Reports */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.report-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.report-card h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.report-content {
    padding: 1rem 0;
}

.report-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.report-header h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.report-header p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.report-summary {
    margin-bottom: 1.5rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--bg-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
}

.summary-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.summary-value {
    font-weight: bold;
    color: var(--text-color);
}

.summary-value.revenue {
    color: var(--success-color);
}

.summary-value.expense {
    color: var(--danger-color);
}

.summary-value.profit {
    color: var(--success-color);
}

.summary-value.loss {
    color: var(--danger-color);
}

.report-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: var(--border-radius);
}

.report-section h5 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.product-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
}

.product-name {
    font-weight: 500;
    color: var(--text-color);
}

.product-stats {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.customer-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.customer-stats p {
    padding: 0.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    margin: 0;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.setting-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.setting-card h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-item input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-color);
    color: var(--text-color);
    transition: var(--transition);
}

.setting-item input:focus {
    outline: none;
    border-color: var(--secondary-color);
}

/* Large Modal */
.large-modal {
    max-width: 800px;
    width: 95%;
}

.invoice-details-view {
    max-height: 70vh;
    overflow-y: auto;
}

.invoice-preview {
    padding: 1rem;
}

.invoice-header-preview {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.invoice-header-preview h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.customer-info,
.session-info,
.purchases-info {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: var(--border-radius);
}

.customer-info h3,
.session-info h3,
.purchases-info h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.purchases-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.purchases-table th,
.purchases-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.purchases-table th {
    background: var(--surface-color);
    font-weight: bold;
    color: var(--primary-color);
}

.invoice-summary {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
}

.invoice-summary .summary-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.invoice-summary .summary-row:last-child {
    border-bottom: none;
}

.invoice-summary .total-row {
    border-top: 2px solid var(--primary-color);
    margin-top: 0.5rem;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.discount-row {
    color: var(--warning-color);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
