"""
واجهة التقارير والجرد
Reports and Inventory Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning,
    format_currency, format_datetime, get_current_date_string
)
from utils.scroll_helper import enable_scroll_for_all_children

class ReportsFrame:
    """إطار التقارير والجرد"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_reports()

        # إضافة دعم السكرول لجميع العناصر
        enable_scroll_for_all_children(self.frame)
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="📊 التقارير والجرد", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار فلاتر التقارير
        self.create_filters_section()
        
        # إطار الإحصائيات السريعة
        self.create_quick_stats_section()
        
        # إطار التقارير التفصيلية
        self.create_detailed_reports_section()
    
    def create_filters_section(self):
        """إنشاء قسم الفلاتر"""
        filters_frame = ttk.LabelFrame(self.frame, text="🔍 فلاتر التقارير", padding=10)
        filters_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول - فلاتر التاريخ
        date_frame = ttk.Frame(filters_frame)
        date_frame.pack(fill=tk.X, pady=5)
        
        # نوع التقرير
        ttk.Label(date_frame, text="نوع التقرير:").pack(side=tk.LEFT, padx=5)
        self.report_type_var = tk.StringVar(value="يومي")
        self.report_type_combo = ttk.Combobox(date_frame, textvariable=self.report_type_var,
                                            values=["يومي", "أسبوعي", "شهري", "مخصص"], 
                                            state="readonly", width=10)
        self.report_type_combo.pack(side=tk.LEFT, padx=5)
        self.report_type_combo.bind('<<ComboboxSelected>>', self.on_report_type_change)
        
        # من تاريخ
        ttk.Label(date_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=(20, 5))
        self.from_date_var = tk.StringVar(value=get_current_date_string())
        self.from_date_entry = ttk.Entry(date_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        # إلى تاريخ
        ttk.Label(date_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=get_current_date_string())
        self.to_date_entry = ttk.Entry(date_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(date_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(
            buttons_frame, 
            text="📊 إنشاء التقرير", 
            command=self.generate_report
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="📄 تصدير", 
            command=self.export_report
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_reports
        ).pack(side=tk.LEFT, padx=2)
    
    def create_quick_stats_section(self):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(self.frame, text="📈 الإحصائيات السريعة", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إطار البطاقات
        cards_frame = ttk.Frame(stats_frame)
        cards_frame.pack(fill=tk.X)
        
        # بطاقة إجمالي المبيعات
        self.total_sales_card = self.create_stat_card(
            cards_frame, "💰 إجمالي المبيعات", "0.00 جنيه", "#4CAF50"
        )
        self.total_sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد الجلسات
        self.sessions_count_card = self.create_stat_card(
            cards_frame, "⏰ عدد الجلسات", "0", "#2196F3"
        )
        self.sessions_count_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد العملاء
        self.customers_count_card = self.create_stat_card(
            cards_frame, "👥 عدد العملاء", "0", "#FF9800"
        )
        self.customers_count_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة متوسط الجلسة
        self.avg_session_card = self.create_stat_card(
            cards_frame, "📊 متوسط الجلسة", "0.00 جنيه", "#9C27B0"
        )
        self.avg_session_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.Frame(parent, relief=tk.RAISED, borderwidth=1)
        
        # عنوان البطاقة
        title_label = ttk.Label(
            card_frame, 
            text=title, 
            font=FONTS['default'],
            foreground=color
        )
        title_label.pack(pady=5)
        
        # قيمة البطاقة
        value_label = ttk.Label(
            card_frame, 
            text=value, 
            font=FONTS['heading']
        )
        value_label.pack(pady=5)
        
        # حفظ مرجع للتحديث لاحقاً
        card_frame.value_label = value_label
        
        return card_frame
    
    def create_detailed_reports_section(self):
        """إنشاء قسم التقارير التفصيلية"""
        reports_frame = ttk.LabelFrame(self.frame, text="📋 التقارير التفصيلية", padding=10)
        reports_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # دفتر التبويبات للتقارير المختلفة
        self.reports_notebook = ttk.Notebook(reports_frame)
        self.reports_notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب تقرير المبيعات
        self.create_sales_report_tab()
        
        # تبويب تقرير الجلسات
        self.create_sessions_report_tab()
        
        # تبويب تقرير العملاء
        self.create_customers_report_tab()
        
        # تبويب تقرير المنتجات
        self.create_products_report_tab()
    
    def create_sales_report_tab(self):
        """إنشاء تبويب تقرير المبيعات"""
        sales_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(sales_frame, text="💰 المبيعات")
        
        # جدول المبيعات
        columns = {
            'date': {'text': 'التاريخ', 'width': 100},
            'sessions_count': {'text': 'عدد الجلسات', 'width': 100},
            'sessions_revenue': {'text': 'إيرادات الجلسات', 'width': 120},
            'products_revenue': {'text': 'إيرادات المنتجات', 'width': 120},
            'total_revenue': {'text': 'إجمالي الإيرادات', 'width': 120},
            'customers_count': {'text': 'عدد العملاء', 'width': 100}
        }
        
        self.sales_tree = ttk.Treeview(sales_frame)
        self.setup_treeview(self.sales_tree, columns)
        
        # شريط التمرير
        sales_scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)
        
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_sessions_report_tab(self):
        """إنشاء تبويب تقرير الجلسات"""
        sessions_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(sessions_frame, text="⏰ الجلسات")
        
        # جدول الجلسات
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'end_time': {'text': 'وقت النهاية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'amount': {'text': 'المبلغ', 'width': 100}
        }
        
        self.sessions_tree = ttk.Treeview(sessions_frame)
        self.setup_treeview(self.sessions_tree, columns)
        
        # شريط التمرير
        sessions_scrollbar = ttk.Scrollbar(sessions_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_customers_report_tab(self):
        """إنشاء تبويب تقرير العملاء"""
        customers_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(customers_frame, text="👥 العملاء")
        
        # جدول العملاء
        columns = {
            'name': {'text': 'اسم العميل', 'width': 150},
            'sessions_count': {'text': 'عدد الجلسات', 'width': 100},
            'total_hours': {'text': 'إجمالي الساعات', 'width': 100},
            'total_spent': {'text': 'إجمالي المبلغ', 'width': 120},
            'avg_session': {'text': 'متوسط الجلسة', 'width': 100},
            'last_visit': {'text': 'آخر زيارة', 'width': 120}
        }
        
        self.customers_tree = ttk.Treeview(customers_frame)
        self.setup_treeview(self.customers_tree, columns)
        
        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)
        
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        customers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_products_report_tab(self):
        """إنشاء تبويب تقرير المنتجات"""
        products_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(products_frame, text="🛍️ المنتجات")
        
        # جدول المنتجات
        columns = {
            'name': {'text': 'اسم المنتج', 'width': 150},
            'category': {'text': 'الفئة', 'width': 100},
            'sold_quantity': {'text': 'الكمية المباعة', 'width': 100},
            'revenue': {'text': 'الإيرادات', 'width': 100},
            'profit': {'text': 'الربح', 'width': 100},
            'remaining_stock': {'text': 'المخزون المتبقي', 'width': 120}
        }
        
        self.products_tree = ttk.Treeview(products_frame)
        self.setup_treeview(self.products_tree, columns)
        
        # شريط التمرير
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def on_report_type_change(self, event=None):
        """عند تغيير نوع التقرير"""
        report_type = self.report_type_var.get()
        today = datetime.now()
        
        if report_type == "يومي":
            self.from_date_var.set(today.strftime("%Y-%m-%d"))
            self.to_date_var.set(today.strftime("%Y-%m-%d"))
        elif report_type == "أسبوعي":
            week_start = today - timedelta(days=today.weekday())
            week_end = week_start + timedelta(days=6)
            self.from_date_var.set(week_start.strftime("%Y-%m-%d"))
            self.to_date_var.set(week_end.strftime("%Y-%m-%d"))
        elif report_type == "شهري":
            month_start = today.replace(day=1)
            next_month = month_start.replace(month=month_start.month + 1) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1)
            month_end = next_month - timedelta(days=1)
            self.from_date_var.set(month_start.strftime("%Y-%m-%d"))
            self.to_date_var.set(month_end.strftime("%Y-%m-%d"))
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()
            
            # تحديث الإحصائيات السريعة
            self.update_quick_stats(from_date, to_date)
            
            # تحديث التقارير التفصيلية
            self.update_detailed_reports(from_date, to_date)
            
            show_success("تم إنشاء التقرير بنجاح")
            
        except Exception as e:
            show_error(f"خطأ في إنشاء التقرير: {e}")
    
    def update_quick_stats(self, from_date, to_date):
        """تحديث الإحصائيات السريعة"""
        try:
            # حساب الإحصائيات الحقيقية من قاعدة البيانات

            # إجمالي الإيرادات من الجلسات المنتهية + المشتريات
            completed_sessions = self.db.get_completed_sessions()

            # حساب إجمالي الإيرادات (جلسات + مشتريات)
            total_sessions_revenue = 0
            total_purchases_revenue = 0

            for session in completed_sessions:
                # إضافة مبلغ الجلسة
                total_sessions_revenue += session.total_amount

                # إضافة مبلغ المشتريات
                purchases = self.db.get_session_purchases(session.id)
                session_purchases_total = sum(p.total_price for p in purchases)
                total_purchases_revenue += session_purchases_total

            # الإجمالي الكلي
            total_revenue = total_sessions_revenue + total_purchases_revenue

            # عدد الجلسات
            total_sessions = len(completed_sessions)

            # عدد العملاء النشطين
            customers = self.db.get_customers()
            active_customers = len([c for c in customers if c.is_active])

            # متوسط قيمة الجلسة (شامل المشتريات)
            avg_session = total_revenue / total_sessions if total_sessions > 0 else 0

            # حساب المصروفات
            expenses = self.db.get_expenses()
            total_expenses = sum(e.amount for e in expenses)

            # حساب الربح الصافي
            net_profit = total_revenue - total_expenses

            # تحديث الواجهة
            self.total_sales_card.value_label.config(text=f"{total_revenue:.2f} جنيه")
            self.sessions_count_card.value_label.config(text=str(total_sessions))
            self.customers_count_card.value_label.config(text=str(active_customers))
            self.avg_session_card.value_label.config(text=f"{avg_session:.2f} جنيه")

            # إضافة معلومات المصروفات والأرباح (إذا كانت متاحة)
            if hasattr(self, 'total_expenses_card'):
                self.total_expenses_card.value_label.config(text=f"{total_expenses:.2f} جنيه")
            if hasattr(self, 'net_profit_card'):
                self.net_profit_card.value_label.config(text=f"{net_profit:.2f} جنيه")

            # طباعة تفاصيل للتشخيص
            print(f"📊 تحديث التقارير:")
            print(f"   إيرادات الجلسات: {total_sessions_revenue:.2f} جنيه")
            print(f"   إيرادات المشتريات: {total_purchases_revenue:.2f} جنيه")
            print(f"   الإجمالي الكلي: {total_revenue:.2f} جنيه")
            print(f"   عدد الجلسات: {total_sessions}")
            print(f"   المصروفات: {total_expenses:.2f} جنيه")
            print(f"   الربح الصافي: {net_profit:.2f} جنيه")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
            # في حالة الخطأ، عرض أصفار
            self.total_sales_card.value_label.config(text="0.00 جنيه")
            self.sessions_count_card.value_label.config(text="0")
            self.customers_count_card.value_label.config(text="0")
            self.avg_session_card.value_label.config(text="0.00 جنيه")
    
    def update_detailed_reports(self, from_date, to_date):
        """تحديث التقارير التفصيلية"""
        try:
            # مسح البيانات الحالية
            for tree in [self.sales_tree, self.sessions_tree, self.customers_tree, self.products_tree]:
                for item in tree.get_children():
                    tree.delete(item)

            # إضافة البيانات الحقيقية
            self.add_real_data()

        except Exception as e:
            print(f"خطأ في تحديث التقارير التفصيلية: {e}")
    
    def add_real_data(self):
        """إضافة البيانات الحقيقية من قاعدة البيانات"""
        try:
            # 1. بيانات المبيعات الحقيقية (الجلسات + المشتريات)
            completed_sessions = self.db.get_completed_sessions()

            for session in completed_sessions:
                # حساب إجمالي الجلسة مع المشتريات
                purchases = self.db.get_session_purchases(session.id)
                purchases_total = sum(p.total_price for p in purchases)
                session_total = session.total_amount + purchases_total

                # إضافة بيانات المبيعات
                self.sales_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    format_currency(session.total_amount),  # مبلغ الجلسة
                    format_currency(purchases_total),       # مبلغ المشتريات
                    format_currency(session_total)          # الإجمالي
                ))

            # 2. بيانات الجلسات الحقيقية
            for session in completed_sessions:
                if session.end_time:
                    from utils.helpers import calculate_time_difference
                    time_diff = calculate_time_difference(session.start_time, session.end_time)
                    duration_str = time_diff['formatted']
                else:
                    duration_str = "غير محدد"

                # حساب الإجمالي مع المشتريات
                purchases = self.db.get_session_purchases(session.id)
                purchases_total = sum(p.total_price for p in purchases)
                total_with_purchases = session.total_amount + purchases_total

                self.sessions_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    format_datetime(session.end_time) if session.end_time else "غير محدد",
                    duration_str,
                    format_currency(total_with_purchases)  # الإجمالي مع المشتريات
                ))

            # 3. بيانات العملاء الحقيقية مع إحصائيات دقيقة
            customers = self.db.get_customers()
            all_sessions = self.db.get_all_sessions()

            for customer in customers:
                if customer.is_active:
                    # حساب إحصائيات العميل الحقيقية
                    customer_sessions = [s for s in all_sessions if s.customer_id == customer.id and not s.is_active]
                    total_sessions = len(customer_sessions)

                    total_hours = sum(s.total_hours for s in customer_sessions)
                    total_spent = 0
                    last_visit = "لا توجد زيارات"

                    # حساب إجمالي المبلغ المنفق (جلسات + مشتريات)
                    for session in customer_sessions:
                        total_spent += session.total_amount
                        purchases = self.db.get_session_purchases(session.id)
                        total_spent += sum(p.total_price for p in purchases)

                    # آخر زيارة
                    if customer_sessions:
                        latest_session = max(customer_sessions, key=lambda s: s.start_time)
                        last_visit = format_datetime(latest_session.start_time)

                    self.customers_tree.insert('', 'end', values=(
                        customer.name,
                        total_sessions,
                        f"{total_hours:.1f} ساعة",
                        format_currency(total_spent),
                        format_currency(total_spent / total_sessions if total_sessions > 0 else 0),
                        last_visit
                    ))

            # 4. بيانات المنتجات الحقيقية مع مبيعات دقيقة
            products = self.db.get_products()
            all_purchases = []

            # جمع جميع المشتريات
            for session in completed_sessions:
                session_purchases = self.db.get_session_purchases(session.id)
                all_purchases.extend(session_purchases)

            for product in products:
                if product.is_active:
                    # حساب المبيعات الحقيقية للمنتج
                    product_purchases = [p for p in all_purchases if p.product_id == product.id]

                    sold_quantity = sum(p.quantity for p in product_purchases)
                    revenue = sum(p.total_price for p in product_purchases)

                    # حساب الربح (الإيرادات - التكلفة)
                    cost = sold_quantity * (product.cost if hasattr(product, 'cost') and product.cost else product.price * 0.7)
                    profit = revenue - cost

                    self.products_tree.insert('', 'end', values=(
                        product.name,
                        product.category or "غير محدد",
                        sold_quantity,
                        format_currency(revenue),
                        format_currency(profit),
                        product.quantity
                    ))

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            # في حالة الخطأ، عرض رسالة
            self.sessions_tree.insert('', 'end', values=("-", "لا توجد بيانات", "-", "-", "-", "-"))
            self.customers_tree.insert('', 'end', values=("-", "-", "-", "-", "-", "-"))
            self.products_tree.insert('', 'end', values=("-", "-", "-", "-", "-", "-"))
    
    def refresh_reports(self):
        """تحديث التقارير"""
        self.generate_report()
    
    def export_report(self):
        """تصدير التقرير"""
        show_warning("سيتم تطوير ميزة التصدير قريباً")
