// Reports Management System
class ReportsManager {
    constructor() {
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.generateAllReports();
    }

    // Generate all reports
    async generateAllReports() {
        await this.generateDailyReport();
        await this.generateWeeklyReport();
        await this.generateMonthlyReport();
    }

    // Generate daily report
    async generateDailyReport() {
        try {
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

            const data = await this.getReportData(startOfDay, endOfDay);
            this.renderDailyReport(data);
        } catch (error) {
            console.error('Error generating daily report:', error);
        }
    }

    // Generate weekly report
    async generateWeeklyReport() {
        try {
            const today = new Date();
            const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
            const endOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() + 7);

            const data = await this.getReportData(startOfWeek, endOfWeek);
            this.renderWeeklyReport(data);
        } catch (error) {
            console.error('Error generating weekly report:', error);
        }
    }

    // Generate monthly report
    async generateMonthlyReport() {
        try {
            const today = new Date();
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

            const data = await this.getReportData(startOfMonth, endOfMonth);
            this.renderMonthlyReport(data);
        } catch (error) {
            console.error('Error generating monthly report:', error);
        }
    }

    // Get report data for date range
    async getReportData(startDate, endDate) {
        try {
            // Get invoices
            const allInvoices = await storage.getAll('invoices');
            const invoices = allInvoices.filter(invoice => {
                const invoiceDate = new Date(invoice.createdAt);
                return invoiceDate >= startDate && invoiceDate < endDate;
            });

            // Get shifts
            const allShifts = await storage.getAll('shifts');
            const shifts = allShifts.filter(shift => {
                const shiftDate = new Date(shift.startTime);
                return shiftDate >= startDate && shiftDate < endDate;
            });

            // Get customers
            const allCustomers = await storage.getAll('customers');
            const customers = allCustomers.filter(customer => {
                const customerDate = new Date(customer.createdAt);
                return customerDate >= startDate && customerDate < endDate;
            });

            // Calculate statistics
            const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.finalAmount, 0);
            const totalInvoices = invoices.length;
            const totalCustomers = customers.length;
            const totalExpenses = shifts.reduce((sum, shift) => sum + (shift.totalExpenses || 0), 0);
            const netProfit = totalRevenue - totalExpenses;

            // Product sales analysis
            const productSales = this.analyzeProductSales(invoices);
            
            // Customer analysis
            const customerAnalysis = this.analyzeCustomers(customers);
            
            // Time analysis
            const timeAnalysis = this.analyzeTimeUsage(invoices);

            return {
                period: { startDate, endDate },
                summary: {
                    totalRevenue,
                    totalInvoices,
                    totalCustomers,
                    totalExpenses,
                    netProfit,
                    averageInvoiceValue: totalInvoices > 0 ? totalRevenue / totalInvoices : 0
                },
                productSales,
                customerAnalysis,
                timeAnalysis,
                shifts,
                invoices
            };
        } catch (error) {
            console.error('Error getting report data:', error);
            return null;
        }
    }

    // Analyze product sales
    analyzeProductSales(invoices) {
        const productStats = {};
        
        invoices.forEach(invoice => {
            invoice.purchases.forEach(purchase => {
                const productName = purchase.productName;
                if (!productStats[productName]) {
                    productStats[productName] = {
                        name: productName,
                        quantity: 0,
                        revenue: 0,
                        count: 0
                    };
                }
                
                productStats[productName].quantity += purchase.quantity;
                productStats[productName].revenue += purchase.total;
                productStats[productName].count += 1;
            });
        });

        // Convert to array and sort by revenue
        const productArray = Object.values(productStats);
        productArray.sort((a, b) => b.revenue - a.revenue);

        return {
            topProducts: productArray.slice(0, 10),
            totalProducts: productArray.length,
            totalProductRevenue: productArray.reduce((sum, product) => sum + product.revenue, 0)
        };
    }

    // Analyze customers
    analyzeCustomers(customers) {
        const totalCustomers = customers.length;
        const activeCustomers = customers.filter(c => c.status === 'active').length;
        const completedSessions = customers.filter(c => c.status === 'completed').length;
        
        const totalTime = customers.reduce((sum, customer) => sum + (customer.totalTime || 0), 0);
        const averageSessionTime = completedSessions > 0 ? totalTime / completedSessions : 0;
        
        const totalSpent = customers.reduce((sum, customer) => sum + (customer.totalCost || 0), 0);
        const averageSpending = completedSessions > 0 ? totalSpent / completedSessions : 0;

        return {
            totalCustomers,
            activeCustomers,
            completedSessions,
            averageSessionTime,
            averageSpending,
            totalTime,
            totalSpent
        };
    }

    // Analyze time usage
    analyzeTimeUsage(invoices) {
        const hourlyStats = {};
        
        invoices.forEach(invoice => {
            const hour = new Date(invoice.createdAt).getHours();
            if (!hourlyStats[hour]) {
                hourlyStats[hour] = {
                    hour,
                    invoices: 0,
                    revenue: 0
                };
            }
            
            hourlyStats[hour].invoices += 1;
            hourlyStats[hour].revenue += invoice.finalAmount;
        });

        // Convert to array and sort by hour
        const hourlyArray = Object.values(hourlyStats);
        hourlyArray.sort((a, b) => a.hour - b.hour);

        // Find peak hours
        const peakHour = hourlyArray.reduce((peak, current) => 
            current.revenue > peak.revenue ? current : peak, 
            { hour: 0, revenue: 0 }
        );

        return {
            hourlyStats: hourlyArray,
            peakHour,
            totalHours: hourlyArray.length
        };
    }

    // Render daily report
    renderDailyReport(data) {
        const dailyReport = document.getElementById('dailyReport');
        if (!dailyReport || !data) return;

        dailyReport.innerHTML = `
            <div class="report-content">
                <div class="report-header">
                    <h4>تقرير اليوم - ${this.formatDate(new Date())}</h4>
                </div>
                
                <div class="report-summary">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الإيرادات:</span>
                            <span class="summary-value revenue">${data.summary.totalRevenue.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الفواتير:</span>
                            <span class="summary-value">${data.summary.totalInvoices}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد العملاء:</span>
                            <span class="summary-value">${data.summary.totalCustomers}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">المصاريف:</span>
                            <span class="summary-value expense">${data.summary.totalExpenses.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">صافي الربح:</span>
                            <span class="summary-value ${data.summary.netProfit >= 0 ? 'profit' : 'loss'}">
                                ${data.summary.netProfit.toFixed(2)} جنيه
                            </span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">متوسط الفاتورة:</span>
                            <span class="summary-value">${data.summary.averageInvoiceValue.toFixed(2)} جنيه</span>
                        </div>
                    </div>
                </div>

                ${data.productSales.topProducts.length > 0 ? `
                    <div class="report-section">
                        <h5>أفضل المنتجات مبيعاً</h5>
                        <div class="products-list">
                            ${data.productSales.topProducts.slice(0, 5).map(product => `
                                <div class="product-item">
                                    <span class="product-name">${product.name}</span>
                                    <span class="product-stats">${product.quantity} قطعة - ${product.revenue.toFixed(2)} جنيه</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="report-actions">
                    <button class="btn btn-sm btn-primary" onclick="reportsManager.printReport('daily')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-sm btn-success" onclick="reportsManager.exportReport('daily')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        `;
    }

    // Render weekly report
    renderWeeklyReport(data) {
        const weeklyReport = document.getElementById('weeklyReport');
        if (!weeklyReport || !data) return;

        const weekStart = this.formatDate(data.period.startDate);
        const weekEnd = this.formatDate(new Date(data.period.endDate.getTime() - 1));

        weeklyReport.innerHTML = `
            <div class="report-content">
                <div class="report-header">
                    <h4>التقرير الأسبوعي</h4>
                    <p>${weekStart} - ${weekEnd}</p>
                </div>
                
                <div class="report-summary">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الإيرادات:</span>
                            <span class="summary-value revenue">${data.summary.totalRevenue.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الفواتير:</span>
                            <span class="summary-value">${data.summary.totalInvoices}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد العملاء:</span>
                            <span class="summary-value">${data.summary.totalCustomers}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">صافي الربح:</span>
                            <span class="summary-value ${data.summary.netProfit >= 0 ? 'profit' : 'loss'}">
                                ${data.summary.netProfit.toFixed(2)} جنيه
                            </span>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h5>إحصائيات العملاء</h5>
                    <div class="customer-stats">
                        <p>متوسط وقت الجلسة: ${this.formatTime(data.customerAnalysis.averageSessionTime)}</p>
                        <p>متوسط الإنفاق: ${data.customerAnalysis.averageSpending.toFixed(2)} جنيه</p>
                        <p>إجمالي الوقت: ${this.formatTime(data.customerAnalysis.totalTime)}</p>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-sm btn-primary" onclick="reportsManager.printReport('weekly')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-sm btn-success" onclick="reportsManager.exportReport('weekly')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        `;
    }

    // Render monthly report
    renderMonthlyReport(data) {
        const monthlyReport = document.getElementById('monthlyReport');
        if (!monthlyReport || !data) return;

        const monthName = data.period.startDate.toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' });

        monthlyReport.innerHTML = `
            <div class="report-content">
                <div class="report-header">
                    <h4>التقرير الشهري - ${monthName}</h4>
                </div>
                
                <div class="report-summary">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الإيرادات:</span>
                            <span class="summary-value revenue">${data.summary.totalRevenue.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الفواتير:</span>
                            <span class="summary-value">${data.summary.totalInvoices}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد العملاء:</span>
                            <span class="summary-value">${data.summary.totalCustomers}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">المصاريف:</span>
                            <span class="summary-value expense">${data.summary.totalExpenses.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">صافي الربح:</span>
                            <span class="summary-value ${data.summary.netProfit >= 0 ? 'profit' : 'loss'}">
                                ${data.summary.netProfit.toFixed(2)} جنيه
                            </span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الشيفتات:</span>
                            <span class="summary-value">${data.shifts.length}</span>
                        </div>
                    </div>
                </div>

                ${data.timeAnalysis.peakHour.hour ? `
                    <div class="report-section">
                        <h5>تحليل الأوقات</h5>
                        <p>أفضل ساعة: ${data.timeAnalysis.peakHour.hour}:00 - ${data.timeAnalysis.peakHour.revenue.toFixed(2)} جنيه</p>
                    </div>
                ` : ''}

                <div class="report-actions">
                    <button class="btn btn-sm btn-primary" onclick="reportsManager.printReport('monthly')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-sm btn-success" onclick="reportsManager.exportReport('monthly')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        `;
    }

    // Generate custom report
    async generateCustomReport(startDate, endDate) {
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            end.setDate(end.getDate() + 1); // Include end date

            const data = await this.getReportData(start, end);
            return data;
        } catch (error) {
            console.error('Error generating custom report:', error);
            return null;
        }
    }

    // Print report
    printReport(reportType) {
        const reportElement = document.getElementById(`${reportType}Report`);
        if (!reportElement) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير ${this.getReportTypeTitle(reportType)}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .report-header { text-align: center; margin-bottom: 30px; }
                    .summary-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; }
                    .summary-item { display: flex; justify-content: space-between; padding: 5px; border-bottom: 1px solid #eee; }
                    .revenue { color: green; font-weight: bold; }
                    .expense { color: red; }
                    .profit { color: green; font-weight: bold; }
                    .loss { color: red; font-weight: bold; }
                    @media print { .report-actions { display: none; } }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>مقهى الأصدقاء</h1>
                    <h2>تقرير ${this.getReportTypeTitle(reportType)}</h2>
                    <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-EG')}</p>
                </div>
                ${reportElement.innerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    // Export report
    async exportReport(reportType) {
        try {
            let data;
            
            switch (reportType) {
                case 'daily':
                    const today = new Date();
                    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
                    data = await this.getReportData(startOfDay, endOfDay);
                    break;
                case 'weekly':
                    const startOfWeek = new Date();
                    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(endOfWeek.getDate() + 7);
                    data = await this.getReportData(startOfWeek, endOfWeek);
                    break;
                case 'monthly':
                    const startOfMonth = new Date();
                    startOfMonth.setDate(1);
                    const endOfMonth = new Date(startOfMonth);
                    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
                    data = await this.getReportData(startOfMonth, endOfMonth);
                    break;
            }

            if (data) {
                const exportData = {
                    reportType: this.getReportTypeTitle(reportType),
                    generatedAt: new Date().toISOString(),
                    period: {
                        start: data.period.startDate.toISOString(),
                        end: data.period.endDate.toISOString()
                    },
                    summary: data.summary,
                    productSales: data.productSales,
                    customerAnalysis: data.customerAnalysis,
                    timeAnalysis: data.timeAnalysis
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `تقرير_${reportType}_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('Error exporting report:', error);
            alert('حدث خطأ أثناء تصدير التقرير');
        }
    }

    // Get report type title
    getReportTypeTitle(reportType) {
        const titles = {
            'daily': 'يومي',
            'weekly': 'أسبوعي',
            'monthly': 'شهري'
        };
        return titles[reportType] || reportType;
    }

    // Generate custom date range report
    async generateCustomReport(startDate, endDate) {
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            end.setDate(end.getDate() + 1); // Include end date

            const data = await this.getReportData(start, end);
            this.showCustomReportModal(data, startDate, endDate);

            return data;
        } catch (error) {
            console.error('Error generating custom report:', error);
            return null;
        }
    }

    // Show custom report modal
    showCustomReportModal(data, startDate, endDate) {
        if (!data) return;

        const modalHTML = `
            <div class="modal large-modal">
                <div class="modal-header">
                    <h3>تقرير مخصص: ${this.formatDate(new Date(startDate))} - ${this.formatDate(new Date(endDate))}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="custom-report-content">
                        ${this.generateCustomReportHTML(data)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="reportsManager.printCustomReport('${startDate}', '${endDate}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-success" onclick="reportsManager.exportCustomReport('${startDate}', '${endDate}')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Generate custom report HTML
    generateCustomReportHTML(data) {
        return `
            <div class="report-content">
                <div class="report-summary">
                    <h4>الملخص العام</h4>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الإيرادات:</span>
                            <span class="summary-value revenue">${data.summary.totalRevenue.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الفواتير:</span>
                            <span class="summary-value">${data.summary.totalInvoices}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد العملاء:</span>
                            <span class="summary-value">${data.summary.totalCustomers}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">المصاريف:</span>
                            <span class="summary-value expense">${data.summary.totalExpenses.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">صافي الربح:</span>
                            <span class="summary-value ${data.summary.netProfit >= 0 ? 'profit' : 'loss'}">
                                ${data.summary.netProfit.toFixed(2)} جنيه
                            </span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">متوسط الفاتورة:</span>
                            <span class="summary-value">${data.summary.averageInvoiceValue.toFixed(2)} جنيه</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">عدد الشيفتات:</span>
                            <span class="summary-value">${data.shifts.length}</span>
                        </div>
                    </div>
                </div>

                ${data.productSales.topProducts.length > 0 ? `
                    <div class="report-section">
                        <h4>أفضل المنتجات مبيعاً</h4>
                        <table class="products-table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>عدد المرات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.productSales.topProducts.slice(0, 10).map(product => `
                                    <tr>
                                        <td>${product.name}</td>
                                        <td>${product.quantity}</td>
                                        <td>${product.revenue.toFixed(2)} جنيه</td>
                                        <td>${product.count}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}

                <div class="report-section">
                    <h4>إحصائيات العملاء</h4>
                    <div class="customer-stats-grid">
                        <div class="stat-card">
                            <h5>متوسط وقت الجلسة</h5>
                            <p class="stat-number">${this.formatTime(data.customerAnalysis.averageSessionTime)}</p>
                        </div>
                        <div class="stat-card">
                            <h5>متوسط الإنفاق</h5>
                            <p class="stat-number">${data.customerAnalysis.averageSpending.toFixed(2)} جنيه</p>
                        </div>
                        <div class="stat-card">
                            <h5>إجمالي الوقت</h5>
                            <p class="stat-number">${this.formatTime(data.customerAnalysis.totalTime)}</p>
                        </div>
                        <div class="stat-card">
                            <h5>الجلسات المكتملة</h5>
                            <p class="stat-number">${data.customerAnalysis.completedSessions}</p>
                        </div>
                    </div>
                </div>

                ${data.timeAnalysis.hourlyStats.length > 0 ? `
                    <div class="report-section">
                        <h4>تحليل الأوقات</h4>
                        <p><strong>أفضل ساعة:</strong> ${data.timeAnalysis.peakHour.hour}:00 - ${data.timeAnalysis.peakHour.revenue.toFixed(2)} جنيه</p>
                        <div class="hourly-stats">
                            ${data.timeAnalysis.hourlyStats.map(hour => `
                                <div class="hour-stat">
                                    <span class="hour-label">${hour.hour}:00</span>
                                    <span class="hour-invoices">${hour.invoices} فاتورة</span>
                                    <span class="hour-revenue">${hour.revenue.toFixed(2)} جنيه</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Show date range picker modal
    showDateRangeModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>تقرير مخصص</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="dateRangeForm">
                        <div class="form-group">
                            <label>من تاريخ:</label>
                            <input type="date" id="customStartDate" required>
                        </div>
                        <div class="form-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" id="customEndDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="reportsManager.generateCustomReportFromForm()">
                        <i class="fas fa-chart-bar"></i> إنشاء التقرير
                    </button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();

        // Set default dates (last 30 days)
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        document.getElementById('customStartDate').value = startDate.toISOString().split('T')[0];
        document.getElementById('customEndDate').value = endDate.toISOString().split('T')[0];
    }

    // Generate custom report from form
    async generateCustomReportFromForm() {
        const startDate = document.getElementById('customStartDate').value;
        const endDate = document.getElementById('customEndDate').value;

        if (!startDate || !endDate) {
            alert('يرجى تحديد تاريخ البداية والنهاية');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
            return;
        }

        closeModal();
        await this.generateCustomReport(startDate, endDate);
    }

    // Print custom report
    async printCustomReport(startDate, endDate) {
        const data = await this.getReportData(new Date(startDate), new Date(endDate));
        if (!data) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير مخصص - ${startDate} إلى ${endDate}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .report-header { text-align: center; margin-bottom: 30px; }
                    .summary-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px; }
                    .summary-item { display: flex; justify-content: space-between; padding: 5px; border-bottom: 1px solid #eee; }
                    .products-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .products-table th, .products-table td { border: 1px solid #000; padding: 8px; text-align: right; }
                    .products-table th { background-color: #f0f0f0; }
                    .revenue { color: green; font-weight: bold; }
                    .expense { color: red; }
                    .profit { color: green; font-weight: bold; }
                    .loss { color: red; font-weight: bold; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>مقهى الأصدقاء</h1>
                    <h2>تقرير مخصص</h2>
                    <p>من ${this.formatDate(new Date(startDate))} إلى ${this.formatDate(new Date(endDate))}</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-EG')}</p>
                </div>
                ${this.generateCustomReportHTML(data)}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    // Export custom report
    async exportCustomReport(startDate, endDate) {
        const data = await this.getReportData(new Date(startDate), new Date(endDate));
        if (!data) return;

        const exportData = {
            reportType: 'تقرير مخصص',
            dateRange: `${startDate} إلى ${endDate}`,
            generatedAt: new Date().toISOString(),
            summary: data.summary,
            productSales: data.productSales,
            customerAnalysis: data.customerAnalysis,
            timeAnalysis: data.timeAnalysis,
            shifts: data.shifts.map(shift => ({
                employeeName: shift.employeeName,
                date: this.formatDate(new Date(shift.startTime)),
                totalSales: shift.totalSales,
                totalExpenses: shift.totalExpenses,
                netAmount: shift.netAmount
            }))
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `تقرير_مخصص_${startDate}_${endDate}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // Setup event listeners
    setupEventListeners() {
        // Refresh reports every 5 minutes
        setInterval(() => {
            this.generateAllReports();
        }, 5 * 60 * 1000);

        // Add custom report button to reports section
        this.addCustomReportButton();
    }

    // Add custom report button
    addCustomReportButton() {
        const reportsSection = document.getElementById('reports');
        if (!reportsSection) return;

        const existingButton = document.getElementById('customReportBtn');
        if (existingButton) return;

        const sectionHeader = reportsSection.querySelector('.section-header');
        if (sectionHeader) {
            const customReportBtn = document.createElement('button');
            customReportBtn.id = 'customReportBtn';
            customReportBtn.className = 'btn btn-info';
            customReportBtn.innerHTML = '<i class="fas fa-calendar-alt"></i> تقرير مخصص';
            customReportBtn.onclick = () => this.showDateRangeModal();
            sectionHeader.appendChild(customReportBtn);
        }
    }

    // Format time in minutes
    formatTime(minutes) {
        if (minutes < 60) {
            return `${Math.round(minutes)} دقيقة`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = Math.round(minutes % 60);
        return `${hours}:${remainingMinutes.toString().padStart(2, '0')} ساعة`;
    }

    // Format date
    formatDate(date) {
        return date.toLocaleDateString('ar-EG');
    }
}

// Initialize reports manager
const reportsManager = new ReportsManager();
