#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظائف التقارير والمبيعات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_reports_calculations():
    """اختبار حسابات التقارير"""
    print("📊 اختبار حسابات التقارير...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # 1. اختبار الجلسات المنتهية
        print("\n🎮 اختبار الجلسات المنتهية:")
        completed_sessions = db.get_completed_sessions()
        print(f"   عدد الجلسات المنتهية: {len(completed_sessions)}")
        
        total_sessions_revenue = 0
        total_purchases_revenue = 0
        
        for session in completed_sessions:
            total_sessions_revenue += session.total_amount
            
            # حساب المشتريات لكل جلسة
            purchases = db.get_session_purchases(session.id)
            session_purchases_total = sum(p.total_price for p in purchases)
            total_purchases_revenue += session_purchases_total
            
            print(f"   جلسة #{session.id}: {session.total_amount:.2f} + {session_purchases_total:.2f} = {session.total_amount + session_purchases_total:.2f} جنيه")
        
        total_revenue = total_sessions_revenue + total_purchases_revenue
        print(f"\n💰 الإجماليات:")
        print(f"   إيرادات الجلسات: {total_sessions_revenue:.2f} جنيه")
        print(f"   إيرادات المشتريات: {total_purchases_revenue:.2f} جنيه")
        print(f"   الإجمالي الكلي: {total_revenue:.2f} جنيه")
        
        # 2. اختبار المصروفات
        print(f"\n💸 اختبار المصروفات:")
        expenses = db.get_expenses()
        total_expenses = sum(e.amount for e in expenses)
        print(f"   عدد المصروفات: {len(expenses)}")
        print(f"   إجمالي المصروفات: {total_expenses:.2f} جنيه")
        
        # 3. حساب الربح الصافي
        net_profit = total_revenue - total_expenses
        print(f"\n📈 الربح الصافي: {net_profit:.2f} جنيه")
        
        # 4. اختبار العملاء
        print(f"\n👥 اختبار العملاء:")
        customers = db.get_customers()
        active_customers = [c for c in customers if c.is_active]
        print(f"   إجمالي العملاء: {len(customers)}")
        print(f"   العملاء النشطين: {len(active_customers)}")
        
        # 5. اختبار المنتجات
        print(f"\n📦 اختبار المنتجات:")
        products = db.get_products()
        active_products = [p for p in products if p.is_active]
        print(f"   إجمالي المنتجات: {len(products)}")
        print(f"   المنتجات النشطة: {len(active_products)}")
        
        # حساب مبيعات المنتجات
        all_purchases = []
        for session in completed_sessions:
            session_purchases = db.get_session_purchases(session.id)
            all_purchases.extend(session_purchases)
        
        print(f"   إجمالي المشتريات: {len(all_purchases)}")
        
        # تفاصيل مبيعات المنتجات
        for product in active_products[:5]:  # أول 5 منتجات
            product_purchases = [p for p in all_purchases if p.product_id == product.id]
            sold_quantity = sum(p.quantity for p in product_purchases)
            revenue = sum(p.total_price for p in product_purchases)
            
            if sold_quantity > 0:
                print(f"   {product.name}: {sold_quantity} وحدة = {revenue:.2f} جنيه")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reports_interface():
    """اختبار واجهة التقارير"""
    print("\n🖼️ اختبار واجهة التقارير...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        from gui.reports import ReportsFrame
        from database.database_manager import DatabaseManager
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("اختبار التقارير")
        root.geometry("1000x700")
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager()
        
        # إنشاء إطار التقارير
        reports_frame = ReportsFrame(root, db)
        
        # إضافة تعليمات
        instructions = tk.Toplevel(root)
        instructions.title("تعليمات اختبار التقارير")
        instructions.geometry("400x300")
        
        instructions_text = """
📊 تعليمات اختبار التقارير:

1. تحقق من الإحصائيات السريعة في الأعلى
2. اضغط "🔄 تحديث" لتحديث البيانات
3. تحقق من الجداول التفصيلية:
   - جدول المبيعات (جلسات + مشتريات)
   - جدول الجلسات المنتهية
   - جدول العملاء مع الإحصائيات
   - جدول المنتجات مع المبيعات

✅ إذا ظهرت البيانات الحقيقية، فالتقارير تعمل!

❌ إذا ظهرت أصفار أو "لا توجد بيانات":
   - تأكد من وجود جلسات منتهية
   - تأكد من وجود مشتريات
   - تأكد من وجود مصروفات
        """
        
        instructions_label = tk.Label(
            instructions, 
            text=instructions_text, 
            justify=tk.LEFT,
            wraplength=350
        )
        instructions_label.pack(padx=10, pady=10)
        
        print("✅ تم فتح واجهة التقارير للاختبار")
        print("📊 تحقق من الإحصائيات والجداول")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التقارير: {e}")
        return False

def create_test_data():
    """إنشاء بيانات اختبار للتقارير"""
    print("\n🔧 إنشاء بيانات اختبار...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # التحقق من وجود بيانات
        customers = db.get_customers()
        products = db.get_products()
        
        if not customers or not products:
            print("❌ لا توجد عملاء أو منتجات! شغل fix_all_problems.py أولاً")
            return False
        
        # إنشاء جلسة تجريبية إذا لم توجد جلسات منتهية
        completed_sessions = db.get_completed_sessions()
        
        if len(completed_sessions) < 3:
            print("📝 إنشاء جلسات تجريبية...")
            
            customer = customers[0]
            
            # إنشاء جلسة تجريبية
            session_id = db.start_session(
                customer_id=customer.id,
                customer_name=customer.name,
                hourly_rate=25.0
            )
            
            # إضافة مشتريات
            if products:
                product1 = products[0]
                product2 = products[1] if len(products) > 1 else products[0]
                
                db.add_purchase(
                    session_id=session_id,
                    product_id=product1.id,
                    product_name=product1.name,
                    quantity=2,
                    unit_price=product1.price + 2.0,  # سعر مخصص
                    notes="مشترى تجريبي للتقارير"
                )
                
                db.add_purchase(
                    session_id=session_id,
                    product_id=product2.id,
                    product_name=product2.name,
                    quantity=1,
                    unit_price=product2.price,
                    notes="مشترى تجريبي آخر"
                )
            
            # إنهاء الجلسة
            import time
            time.sleep(2)  # انتظار قصير
            db.end_session(session_id)
            
            print(f"✅ تم إنشاء جلسة تجريبية: ID={session_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء بيانات الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار وظائف التقارير...")
    print("="*60)
    
    tests = [
        ("إنشاء بيانات الاختبار", create_test_data),
        ("حسابات التقارير", test_reports_calculations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع اختبارات التقارير نجحت!")
        print("\n✅ الميزات التي تعمل:")
        print("   - حساب إيرادات الجلسات")
        print("   - حساب إيرادات المشتريات")
        print("   - حساب إجمالي المبيعات")
        print("   - حساب المصروفات والربح الصافي")
        print("   - إحصائيات العملاء الدقيقة")
        print("   - مبيعات المنتجات الحقيقية")
        
        # اختبار الواجهة
        print("\n🖼️ هل تريد اختبار واجهة التقارير؟ (y/n)")
        try:
            choice = input().lower()
            if choice in ['y', 'yes', 'نعم', 'ن']:
                test_reports_interface()
        except:
            pass
    else:
        print(f"⚠️ {total - passed} اختبارات فشلت")
        print("يرجى مراجعة رسائل الخطأ أعلاه")

if __name__ == "__main__":
    main()
