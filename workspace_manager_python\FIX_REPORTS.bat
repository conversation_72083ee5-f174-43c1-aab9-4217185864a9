@echo off
chcp 65001 >nul
echo ========================================
echo      إصلاح صفحة التقارير والمبيعات
echo ========================================
echo.

echo 🔧 إصلاح قاعدة البيانات وإضافة البيانات...
python fix_all_problems.py
echo.

echo 📊 اختبار وظائف التقارير...
python test_reports_functionality.py
echo.

echo ✅ تم إصلاح صفحة التقارير!
echo.
echo 🎯 المشاكل المحلولة:
echo.
echo   📈 حساب المبيعات الصحيح:
echo      ✅ إيرادات الجلسات + إيرادات المشتريات
echo      ✅ حساب الإجمالي الكلي بدقة
echo      ✅ تحديث تلقائي للمبيعات
echo.
echo   📊 الإحصائيات السريعة:
echo      ✅ إجمالي المبيعات (جلسات + مشتريات)
echo      ✅ عدد الجلسات المنتهية
echo      ✅ عدد العملاء النشطين
echo      ✅ متوسط قيمة الجلسة
echo      ✅ إجمالي المصروفات
echo      ✅ الربح الصافي
echo.
echo   📋 التقارير التفصيلية:
echo      ✅ جدول المبيعات (جلسة + مشتريات + إجمالي)
echo      ✅ جدول الجلسات مع الإجمالي الصحيح
echo      ✅ جدول العملاء مع إحصائيات دقيقة
echo      ✅ جدول المنتجات مع مبيعات حقيقية
echo.
echo   🔄 التحديث التلقائي:
echo      ✅ زر "تحديث" يعمل بشكل صحيح
echo      ✅ تحديث البيانات من قاعدة البيانات
echo      ✅ حساب دقيق للأرباح والخسائر
echo.

echo 📖 كيفية الاستخدام:
echo   1. اذهب إلى صفحة "التقارير"
echo   2. ستظهر الإحصائيات السريعة في الأعلى
echo   3. اضغط "🔄 تحديث" لتحديث البيانات
echo   4. تصفح الجداول التفصيلية أسفل الصفحة
echo   5. جميع الأرقام حقيقية من قاعدة البيانات
echo.

echo اضغط أي مفتاح لتشغيل البرنامج...
pause >nul

echo 🚀 تشغيل البرنامج مع التقارير المحدثة...
python main.py
