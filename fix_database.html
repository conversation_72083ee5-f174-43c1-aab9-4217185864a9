<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - سكون</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 10px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .fix-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            font-weight: bold;
        }
        .fix-button:hover {
            background: #c0392b;
        }
        .fix-button.success {
            background: #27ae60;
        }
        .fix-button.success:hover {
            background: #229954;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح قاعدة البيانات</h1>
            <p>حل سريع لمشاكل قاعدة البيانات والفواتير</p>
        </div>

        <div class="fix-section">
            <h3>🚨 المشاكل الشائعة</h3>
            <div class="warning">
                <strong>الأعراض:</strong><br>
                ❌ خطأ: "One of the specified object stores was not found"<br>
                ❌ الفواتير لا تظهر<br>
                ❌ لا يمكن إضافة موظفين<br>
                ❌ البيانات لا تحفظ
            </div>
        </div>

        <div class="fix-section">
            <h3>🔧 الحلول السريعة</h3>
            
            <div class="step">
                <strong>الخطوة 1:</strong> إعادة تعيين قاعدة البيانات
                <br>
                <button class="fix-button" onclick="resetDatabase()">
                    🗄️ إعادة تعيين قاعدة البيانات
                </button>
                <div id="resetResult" class="result" style="display: none;"></div>
            </div>

            <div class="step">
                <strong>الخطوة 2:</strong> إنشاء بيانات تجريبية
                <br>
                <button class="fix-button" onclick="createTestData()">
                    📊 إنشاء بيانات تجريبية
                </button>
                <div id="testDataResult" class="result" style="display: none;"></div>
            </div>

            <div class="step">
                <strong>الخطوة 3:</strong> اختبار النظام
                <br>
                <button class="fix-button success" onclick="testSystem()">
                    ✅ اختبار النظام
                </button>
                <div id="testResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🚀 فتح النظام</h3>
            <p>بعد إكمال الإصلاحات:</p>
            <button class="fix-button success" onclick="openMainSystem()" style="font-size: 18px; padding: 15px 30px;">
                🎯 فتح نظام سكون
            </button>
        </div>
    </div>

    <script>
        // إعادة تعيين قاعدة البيانات
        async function resetDatabase() {
            const result = document.getElementById('resetResult');
            result.style.display = 'block';
            result.className = 'result info';
            result.innerHTML = '⏳ جاري إعادة تعيين قاعدة البيانات...';

            try {
                // حذف قاعدة البيانات الحالية
                const deleteRequest = indexedDB.deleteDatabase('StudyPlaceDB');
                
                deleteRequest.onsuccess = () => {
                    // مسح localStorage أيضاً
                    const keysToDelete = [];
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && (key.startsWith('customer_') || 
                                   key.startsWith('invoice_') || 
                                   key.startsWith('employee_') || 
                                   key.startsWith('product_') || 
                                   key.startsWith('shift_') || 
                                   key.startsWith('setting_'))) {
                            keysToDelete.push(key);
                        }
                    }
                    
                    keysToDelete.forEach(key => localStorage.removeItem(key));
                    
                    result.className = 'result success';
                    result.innerHTML = '✅ تم إعادة تعيين قاعدة البيانات بنجاح!<br>تم حذف ' + keysToDelete.length + ' عنصر من localStorage';
                };

                deleteRequest.onerror = () => {
                    result.className = 'result error';
                    result.innerHTML = '❌ خطأ في حذف قاعدة البيانات';
                };

            } catch (error) {
                result.className = 'result error';
                result.innerHTML = '❌ خطأ: ' + error.message;
            }
        }

        // إنشاء بيانات تجريبية
        function createTestData() {
            const result = document.getElementById('testDataResult');
            result.style.display = 'block';
            result.className = 'result info';
            result.innerHTML = '⏳ جاري إنشاء البيانات التجريبية...';

            try {
                // إنشاء إعدادات
                const settings = [
                    {key: 'placeName', value: 'سكون'},
                    {key: 'hourlyRate', value: 10},
                    {key: 'dailyRate', value: 65}
                ];

                settings.forEach(setting => {
                    localStorage.setItem(`setting_${setting.key}`, JSON.stringify(setting));
                });

                // إنشاء موظفين
                const employees = [
                    {
                        id: 'emp_001',
                        employeeCode: 'EMP-001',
                        name: 'عمر',
                        position: 'موظف',
                        department: 'العمليات',
                        status: 'active',
                        permissions: {
                            canOpenShift: true,
                            canCloseShift: true,
                            canAddCustomers: true,
                            canEditPrices: true,
                            canViewReports: true,
                            canManageEmployees: true,
                            canManageProducts: true,
                            canDeleteData: true
                        }
                    },
                    {
                        id: 'emp_002',
                        employeeCode: 'EMP-002',
                        name: 'زياد',
                        position: 'موظف',
                        department: 'العمليات',
                        status: 'active',
                        permissions: {
                            canOpenShift: true,
                            canCloseShift: true,
                            canAddCustomers: true,
                            canEditPrices: true,
                            canViewReports: true,
                            canManageEmployees: true,
                            canManageProducts: true,
                            canDeleteData: true
                        }
                    },
                    {
                        id: 'emp_003',
                        employeeCode: 'EMP-003',
                        name: 'مريم',
                        position: 'موظف',
                        department: 'العمليات',
                        status: 'active',
                        permissions: {
                            canOpenShift: true,
                            canCloseShift: true,
                            canAddCustomers: true,
                            canEditPrices: true,
                            canViewReports: true,
                            canManageEmployees: true,
                            canManageProducts: true,
                            canDeleteData: true
                        }
                    }
                ];

                employees.forEach(emp => {
                    localStorage.setItem(`employee_${emp.id}`, JSON.stringify(emp));
                });

                // إنشاء منتجات
                const products = [
                    {
                        id: 'prod_001',
                        code: 'TEA001',
                        name: 'شاي',
                        price: 5,
                        category: 'مشروبات',
                        status: 'active'
                    },
                    {
                        id: 'prod_002',
                        code: 'COFFEE001',
                        name: 'قهوة',
                        price: 8,
                        category: 'مشروبات',
                        status: 'active'
                    }
                ];

                products.forEach(product => {
                    localStorage.setItem(`product_${product.id}`, JSON.stringify(product));
                });

                // إنشاء فواتير تجريبية
                const invoices = [
                    {
                        id: 'inv_001',
                        invoiceNumber: 'INV-24-001',
                        customerName: 'أحمد محمد',
                        customerPhone: '01234567890',
                        serviceType: 'hourly',
                        totalTime: 120,
                        timeCost: 20,
                        purchasesCost: 5,
                        totalCost: 25,
                        finalAmount: 25,
                        status: 'completed',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'inv_002',
                        invoiceNumber: 'INV-24-002',
                        customerName: 'فاطمة علي',
                        customerPhone: '01987654321',
                        serviceType: 'daily',
                        totalTime: 1440,
                        timeCost: 65,
                        purchasesCost: 0,
                        totalCost: 65,
                        finalAmount: 65,
                        status: 'completed',
                        createdAt: new Date().toISOString()
                    }
                ];

                invoices.forEach(invoice => {
                    localStorage.setItem(`invoice_${invoice.id}`, JSON.stringify(invoice));
                });

                result.className = 'result success';
                result.innerHTML = `
                    ✅ تم إنشاء البيانات التجريبية بنجاح!<br>
                    📊 الإحصائيات:<br>
                    - ${settings.length} إعدادات<br>
                    - ${employees.length} موظفين<br>
                    - ${products.length} منتجات<br>
                    - ${invoices.length} فواتير
                `;

            } catch (error) {
                result.className = 'result error';
                result.innerHTML = '❌ خطأ في إنشاء البيانات: ' + error.message;
            }
        }

        // اختبار النظام
        function testSystem() {
            const result = document.getElementById('testResult');
            result.style.display = 'block';
            result.className = 'result info';
            result.innerHTML = '⏳ جاري اختبار النظام...';

            try {
                // فحص البيانات
                let employeeCount = 0;
                let invoiceCount = 0;
                let productCount = 0;
                let settingCount = 0;

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.startsWith('employee_')) employeeCount++;
                    if (key.startsWith('invoice_')) invoiceCount++;
                    if (key.startsWith('product_')) productCount++;
                    if (key.startsWith('setting_')) settingCount++;
                }

                const allGood = employeeCount > 0 && invoiceCount > 0 && productCount > 0 && settingCount > 0;

                if (allGood) {
                    result.className = 'result success';
                    result.innerHTML = `
                        ✅ النظام يعمل بشكل صحيح!<br>
                        📊 البيانات الموجودة:<br>
                        - ${employeeCount} موظف<br>
                        - ${invoiceCount} فاتورة<br>
                        - ${productCount} منتج<br>
                        - ${settingCount} إعداد<br><br>
                        🎉 يمكنك الآن فتح النظام الرئيسي!
                    `;
                } else {
                    result.className = 'result warning';
                    result.innerHTML = `
                        ⚠️ النظام يحتاج إلى بيانات إضافية:<br>
                        - الموظفين: ${employeeCount}<br>
                        - الفواتير: ${invoiceCount}<br>
                        - المنتجات: ${productCount}<br>
                        - الإعدادات: ${settingCount}<br><br>
                        يرجى إنشاء البيانات التجريبية أولاً.
                    `;
                }

            } catch (error) {
                result.className = 'result error';
                result.innerHTML = '❌ خطأ في اختبار النظام: ' + error.message;
            }
        }

        // فتح النظام الرئيسي
        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            testSystem();
        };
    </script>
</body>
</html>
