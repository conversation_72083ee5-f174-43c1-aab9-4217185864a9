#!/bin/bash

# نظام إدارة مكان المذاكرة - Study Place Management
# سكريبت تشغيل السيرفر المحلي

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# طباعة الشعار
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🎓 نظام إدارة مكان المذاكرة                    ║"
    echo "║                      Study Place Management                  ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║  🚀 تشغيل السيرفر المحلي                                       ║"
    echo "║  💾 حفظ آمن للبيانات                                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# فحص Python
check_python() {
    echo -e "${BLUE}🔍 فحص Python...${NC}"
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        echo -e "${GREEN}✅ Python3 مثبت بنجاح${NC}"
        return 0
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        echo -e "${GREEN}✅ Python مثبت بنجاح${NC}"
        return 0
    else
        echo -e "${RED}❌ Python غير مثبت على النظام${NC}"
        echo
        echo -e "${YELLOW}📥 يرجى تثبيت Python من:${NC}"
        echo "   https://www.python.org/downloads/"
        echo
        echo -e "${YELLOW}💡 أو استخدم مدير الحزم:${NC}"
        echo "   Ubuntu/Debian: sudo apt install python3"
        echo "   CentOS/RHEL: sudo yum install python3"
        echo "   macOS: brew install python3"
        echo
        return 1
    fi
}

# فحص الملفات المطلوبة
check_files() {
    echo -e "${BLUE}🔍 فحص الملفات...${NC}"
    
    local missing_files=()
    
    if [[ ! -f "index.html" ]]; then
        missing_files+=("index.html")
    fi
    
    if [[ ! -f "js/app.js" ]]; then
        missing_files+=("js/app.js")
    fi
    
    if [[ ! -f "css/styles.css" ]]; then
        missing_files+=("css/styles.css")
    fi
    
    if [[ ! -f "server.py" ]]; then
        missing_files+=("server.py")
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo -e "${RED}❌ ملفات مفقودة:${NC}"
        for file in "${missing_files[@]}"; do
            echo "   - $file"
        done
        echo
        return 1
    fi
    
    echo -e "${GREEN}✅ جميع الملفات موجودة${NC}"
    return 0
}

# تشغيل السيرفر
start_server() {
    echo
    echo -e "${GREEN}🚀 تشغيل السيرفر...${NC}"
    echo
    
    # جعل الملف قابل للتنفيذ
    chmod +x server.py
    
    # تشغيل السيرفر
    $PYTHON_CMD server.py
}

# الدالة الرئيسية
main() {
    print_banner
    
    # فحص Python
    if ! check_python; then
        echo -e "${RED}❌ فشل في فحص Python${NC}"
        exit 1
    fi
    
    # فحص الملفات
    if ! check_files; then
        echo -e "${RED}❌ ملفات مفقودة! تأكد من وجود جميع ملفات النظام${NC}"
        exit 1
    fi
    
    # تشغيل السيرفر
    start_server
    
    echo
    echo -e "${CYAN}👋 شكراً لاستخدام نظام إدارة مكان المذاكرة${NC}"
}

# تشغيل البرنامج
main "$@"
