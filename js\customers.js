// Customer Management System
class CustomerManager {
    constructor() {
        this.customers = [];
        this.activeCustomers = new Map();
        this.init();
    }

    async init() {
        await this.loadCustomers();
        this.renderCustomers();
        this.setupEventListeners();
    }

    // Load customers from storage
    async loadCustomers() {
        try {
            this.customers = await storage.getAll('customers');
            this.updateActiveCustomers();
        } catch (error) {
            console.error('Error loading customers:', error);
            this.customers = [];
        }
    }

    // Update active customers map
    updateActiveCustomers() {
        this.activeCustomers.clear();
        this.customers.forEach(customer => {
            if (customer.status === 'active') {
                this.activeCustomers.set(customer.id, customer);
            }
        });
    }

    // Add new customer
    async addCustomer(customerData) {
        try {
            const customer = {
                id: storage.generateId(),
                name: customerData.name.trim(),
                phone: customerData.phone?.trim() || '',
                notes: customerData.notes?.trim() || '',
                status: 'active',
                entryTime: new Date().toISOString(),
                exitTime: null,
                totalTime: 0,
                totalCost: 0,
                purchases: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await storage.save('customers', customer);
            this.customers.push(customer);
            this.activeCustomers.set(customer.id, customer);
            
            this.renderCustomers();
            this.updateDashboardStats();
            this.addActivity(`تم إضافة العميل: ${customer.name}`);
            
            return customer;
        } catch (error) {
            console.error('Error adding customer:', error);
            throw error;
        }
    }

    // Update customer
    async updateCustomer(customerId, updates) {
        try {
            const customerIndex = this.customers.findIndex(c => c.id === customerId);
            if (customerIndex === -1) {
                throw new Error('Customer not found');
            }

            const customer = { 
                ...this.customers[customerIndex], 
                ...updates,
                updatedAt: new Date().toISOString()
            };

            await storage.save('customers', customer);
            this.customers[customerIndex] = customer;
            
            if (customer.status === 'active') {
                this.activeCustomers.set(customerId, customer);
            } else {
                this.activeCustomers.delete(customerId);
            }
            
            this.renderCustomers();
            this.updateDashboardStats();
            
            return customer;
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }

    // Delete customer
    async deleteCustomer(customerId) {
        try {
            await storage.delete('customers', customerId);
            this.customers = this.customers.filter(c => c.id !== customerId);
            this.activeCustomers.delete(customerId);
            
            this.renderCustomers();
            this.updateDashboardStats();
            this.addActivity(`تم حذف عميل`);
        } catch (error) {
            console.error('Error deleting customer:', error);
            throw error;
        }
    }

    // Start session for customer
    async startSession(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        await this.updateCustomer(customerId, {
            status: 'active',
            entryTime: new Date().toISOString(),
            exitTime: null,
            totalTime: 0,
            totalCost: 0,
            purchases: []
        });

        this.addActivity(`بدأ جلسة: ${customer.name}`);
    }

    // End session for customer
    async endSession(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.status !== 'active') return;

        const entryTime = new Date(customer.entryTime);
        const exitTime = new Date();
        const totalMinutes = Math.floor((exitTime - entryTime) / (1000 * 60));
        
        // Calculate time cost
        const hourlyRate = await this.getHourlyRate();
        const timeCost = (totalMinutes / 60) * hourlyRate;
        
        // Calculate total cost including purchases
        const purchasesCost = customer.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const totalCost = timeCost + purchasesCost;

        await this.updateCustomer(customerId, {
            status: 'completed',
            exitTime: exitTime.toISOString(),
            totalTime: totalMinutes,
            totalCost: totalCost
        });

        this.addActivity(`انتهت جلسة: ${customer.name} - ${totalMinutes} دقيقة`);
        
        // Generate invoice
        await this.generateInvoice(customerId);
        
        return { totalMinutes, totalCost };
    }

    // Add purchase to customer
    async addPurchase(customerId, productId, quantity = 1) {
        try {
            const customer = this.customers.find(c => c.id === customerId);
            const product = await storage.get('products', productId);
            
            if (!customer || !product) return;

            const purchase = {
                id: storage.generateId(),
                productId: productId,
                productName: product.name,
                productCode: product.code,
                price: product.price,
                quantity: quantity,
                total: product.price * quantity,
                timestamp: new Date().toISOString()
            };

            const purchases = [...customer.purchases, purchase];
            const purchasesCost = purchases.reduce((sum, p) => sum + p.total, 0);
            
            // Calculate time cost
            const entryTime = new Date(customer.entryTime);
            const currentTime = new Date();
            const totalMinutes = Math.floor((currentTime - entryTime) / (1000 * 60));
            const hourlyRate = await this.getHourlyRate();
            const timeCost = (totalMinutes / 60) * hourlyRate;
            
            await this.updateCustomer(customerId, {
                purchases: purchases,
                totalCost: timeCost + purchasesCost,
                totalTime: totalMinutes
            });

            this.addActivity(`إضافة مشتريات: ${product.name} للعميل ${customer.name}`);
            
            return purchase;
        } catch (error) {
            console.error('Error adding purchase:', error);
            throw error;
        }
    }

    // Remove purchase from customer
    async removePurchase(customerId, purchaseId) {
        try {
            const customer = this.customers.find(c => c.id === customerId);
            if (!customer) return;

            const purchases = customer.purchases.filter(p => p.id !== purchaseId);
            const purchasesCost = purchases.reduce((sum, p) => sum + p.total, 0);
            
            // Recalculate total cost
            const entryTime = new Date(customer.entryTime);
            const currentTime = new Date();
            const totalMinutes = Math.floor((currentTime - entryTime) / (1000 * 60));
            const hourlyRate = await this.getHourlyRate();
            const timeCost = (totalMinutes / 60) * hourlyRate;
            
            await this.updateCustomer(customerId, {
                purchases: purchases,
                totalCost: timeCost + purchasesCost
            });

            this.addActivity(`حذف مشتريات للعميل ${customer.name}`);
        } catch (error) {
            console.error('Error removing purchase:', error);
            throw error;
        }
    }

    // Generate invoice for customer
    async generateInvoice(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const invoice = {
            id: storage.generateId(),
            customerId: customerId,
            customerName: customer.name,
            customerPhone: customer.phone,
            entryTime: customer.entryTime,
            exitTime: customer.exitTime,
            totalTime: customer.totalTime,
            purchases: customer.purchases,
            timeCost: customer.totalCost - customer.purchases.reduce((sum, p) => sum + p.total, 0),
            purchasesCost: customer.purchases.reduce((sum, p) => sum + p.total, 0),
            totalCost: customer.totalCost,
            createdAt: new Date().toISOString(),
            status: 'completed'
        };

        await storage.save('invoices', invoice);
        this.addActivity(`تم إنشاء فاتورة للعميل: ${customer.name}`);
        
        return invoice;
    }

    // Get hourly rate from settings
    async getHourlyRate() {
        try {
            const setting = await storage.get('settings', 'hourlyRate');
            return setting ? setting.value : 10; // Default 10 EGP per hour
        } catch (error) {
            return 10;
        }
    }

    // Search customers
    async searchCustomers(searchTerm) {
        if (!searchTerm) {
            this.renderCustomers();
            return;
        }

        const results = await storage.search('customers', searchTerm, ['name', 'phone']);
        this.renderCustomers(results);
    }

    // Render customers
    renderCustomers(customersToRender = null) {
        const customersGrid = document.getElementById('customersGrid');
        if (!customersGrid) return;

        const customers = customersToRender || this.customers;
        
        if (customers.length === 0) {
            customersGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-3x"></i>
                    <h3>لا توجد عملاء</h3>
                    <p>ابدأ بإضافة عميل جديد</p>
                </div>
            `;
            return;
        }

        customersGrid.innerHTML = customers.map(customer => this.createCustomerCard(customer)).join('');
    }

    // Create customer card HTML
    createCustomerCard(customer) {
        const isActive = customer.status === 'active';
        const currentTime = isActive ? this.calculateCurrentTime(customer.entryTime) : customer.totalTime;
        const statusClass = isActive ? 'status-active' : 'status-inactive';
        const statusText = isActive ? 'نشط' : 'مكتمل';

        return `
            <div class="customer-card fade-in">
                <div class="card-header">
                    <div class="card-title">${customer.name}</div>
                    <div class="card-actions">
                        ${isActive ? `
                            <button class="btn btn-sm btn-success" onclick="customerManager.endSession('${customer.id}')" title="إنهاء الجلسة">
                                <i class="fas fa-stop"></i>
                            </button>
                        ` : `
                            <button class="btn btn-sm btn-primary" onclick="customerManager.startSession('${customer.id}')" title="بدء جلسة جديدة">
                                <i class="fas fa-play"></i>
                            </button>
                        `}
                        <button class="btn btn-sm btn-info" onclick="customerManager.showCustomerDetails('${customer.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="customerManager.editCustomer('${customer.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="customerManager.confirmDelete('${customer.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="card-info">
                    <p><strong>الحالة:</strong> <span class="status-badge ${statusClass}">${statusText}</span></p>
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    <p><strong>الوقت:</strong> ${this.formatTime(currentTime)} دقيقة</p>
                    <p><strong>التكلفة:</strong> ${customer.totalCost.toFixed(2)} جنيه</p>
                    ${customer.purchases.length > 0 ? `<p><strong>المشتريات:</strong> ${customer.purchases.length} عنصر</p>` : ''}
                    ${isActive ? `<p><strong>وقت الدخول:</strong> ${this.formatDateTime(customer.entryTime)}</p>` : ''}
                </div>
                
                ${isActive ? `
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="customerManager.showAddPurchaseModal('${customer.id}')">
                            <i class="fas fa-plus"></i> إضافة مشتريات
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Calculate current time for active customer
    calculateCurrentTime(entryTime) {
        const entry = new Date(entryTime);
        const now = new Date();
        return Math.floor((now - entry) / (1000 * 60));
    }

    // Format time in minutes
    formatTime(minutes) {
        if (minutes < 60) {
            return minutes;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}:${remainingMinutes.toString().padStart(2, '0')} ساعة`;
    }

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG');
    }

    // Setup event listeners
    setupEventListeners() {
        // Customer search
        const searchInput = document.getElementById('customerSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchCustomers(e.target.value);
            });
        }

        // Update active customers time every minute
        setInterval(() => {
            if (this.activeCustomers.size > 0) {
                this.renderCustomers();
                this.updateDashboardStats();
            }
        }, 60000); // Update every minute
    }

    // Update dashboard statistics
    updateDashboardStats() {
        const activeCustomersElement = document.getElementById('activeCustomers');
        if (activeCustomersElement) {
            activeCustomersElement.textContent = this.activeCustomers.size;
        }
    }

    // Add activity to recent activity list
    addActivity(message) {
        if (window.activityManager) {
            window.activityManager.addActivity(message);
        }
    }

    // Show customer details modal
    showCustomerDetails(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const modalHTML = `
            <div class="modal large-modal">
                <div class="modal-header">
                    <h3>تفاصيل العميل: ${customer.name}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="customer-details-view">
                        <div class="customer-info">
                            <h4>البيانات الأساسية</h4>
                            <p><strong>الاسم:</strong> ${customer.name}</p>
                            ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                            ${customer.notes ? `<p><strong>الملاحظات:</strong> ${customer.notes}</p>` : ''}
                            <p><strong>الحالة:</strong> ${customer.status === 'active' ? 'نشط' : 'مكتمل'}</p>
                            <p><strong>تاريخ الإنشاء:</strong> ${this.formatDateTime(customer.createdAt)}</p>
                        </div>

                        <div class="session-info">
                            <h4>بيانات الجلسة</h4>
                            <p><strong>وقت الدخول:</strong> ${this.formatDateTime(customer.entryTime)}</p>
                            ${customer.exitTime ? `<p><strong>وقت الخروج:</strong> ${this.formatDateTime(customer.exitTime)}</p>` : ''}
                            <p><strong>إجمالي الوقت:</strong> ${this.formatTime(customer.totalTime)} دقيقة</p>
                            <p><strong>التكلفة الإجمالية:</strong> ${customer.totalCost.toFixed(2)} جنيه</p>
                        </div>

                        ${customer.purchases.length > 0 ? `
                            <div class="purchases-info">
                                <h4>المشتريات (${customer.purchases.length})</h4>
                                <table class="purchases-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>المجموع</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${customer.purchases.map(purchase => `
                                            <tr>
                                                <td>${purchase.productName}</td>
                                                <td>${purchase.quantity}</td>
                                                <td>${purchase.price.toFixed(2)} جنيه</td>
                                                <td>${purchase.total.toFixed(2)} جنيه</td>
                                                <td>${this.formatDateTime(purchase.timestamp)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : '<p>لا توجد مشتريات</p>'}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Edit customer
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // Fill form with customer data
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerNotes').value = customer.notes || '';

        // Store customer ID for update
        document.getElementById('customerForm').dataset.customerId = customerId;

        // Show modal
        showModal('addCustomerModal');
    }

    // Confirm delete customer
    confirmDelete(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
            this.deleteCustomer(customerId);
        }
    }

    // Show add purchase modal
    showAddPurchaseModal(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const products = productManager.getActiveProducts();

        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>إضافة مشتريات - ${customer.name}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="products-selection">
                        <div class="search-bar">
                            <input type="text" id="purchaseProductSearch" placeholder="البحث عن منتج أو كود..." class="search-input">
                        </div>
                        <div class="products-grid" id="purchaseProductsGrid">
                            ${products.map(product => `
                                <div class="product-option" onclick="customerManager.addPurchaseToCustomer('${customerId}', '${product.id}')">
                                    <div class="product-info">
                                        <h4>${product.name}</h4>
                                        <p>الكود: ${product.code}</p>
                                        <p class="product-price">${product.price.toFixed(2)} جنيه</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();

        // Setup search for products
        document.getElementById('purchaseProductSearch').addEventListener('input', (e) => {
            this.filterPurchaseProducts(e.target.value, products);
        });
    }

    // Filter products in purchase modal
    filterPurchaseProducts(searchTerm, products) {
        const filtered = products.filter(product =>
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.code.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const grid = document.getElementById('purchaseProductsGrid');
        if (grid) {
            grid.innerHTML = filtered.map(product => `
                <div class="product-option" onclick="customerManager.addPurchaseToCustomer('${customer.id}', '${product.id}')">
                    <div class="product-info">
                        <h4>${product.name}</h4>
                        <p>الكود: ${product.code}</p>
                        <p class="product-price">${product.price.toFixed(2)} جنيه</p>
                    </div>
                </div>
            `).join('');
        }
    }

    // Add purchase to customer from modal
    async addPurchaseToCustomer(customerId, productId) {
        await this.addPurchase(customerId, productId);
        closeModal();
    }
}

// Initialize customer manager
const customerManager = new CustomerManager();
