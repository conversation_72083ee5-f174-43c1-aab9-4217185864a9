// Customer Management System
class CustomerManager {
    constructor() {
        this.customers = [];
        this.activeCustomers = new Map();
        this.init();
    }

    async init() {
        await this.loadCustomers();
        this.renderCustomers();
        this.setupEventListeners();
    }

    // Load customers from storage
    async loadCustomers() {
        try {
            this.customers = await storage.getAll('customers');
            this.updateActiveCustomers();
        } catch (error) {
            console.error('Error loading customers:', error);
            this.customers = [];
        }
    }

    // Update active customers map
    updateActiveCustomers() {
        this.activeCustomers.clear();
        this.customers.forEach(customer => {
            if (customer.status === 'active') {
                this.activeCustomers.set(customer.id, customer);
            }
        });
    }

    // Add new customer
    async addCustomer(customerData) {
        try {
            const customer = {
                id: storage.generateId(),
                name: customerData.name.trim(),
                phone: customerData.phone?.trim() || '',
                notes: customerData.notes?.trim() || '',
                serviceType: customerData.serviceType || 'hourly', // 'hourly' or 'daily'
                status: 'active',
                entryTime: new Date().toISOString(),
                exitTime: null,
                totalTime: 0,
                totalCost: 0,
                purchases: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await storage.save('customers', customer);
            this.customers.push(customer);
            this.activeCustomers.set(customer.id, customer);
            
            this.renderCustomers();
            this.updateDashboardStats();
            this.addActivity(`تم إضافة العميل: ${customer.name}`);
            
            return customer;
        } catch (error) {
            console.error('Error adding customer:', error);
            throw error;
        }
    }

    // Update customer
    async updateCustomer(customerId, updates) {
        try {
            const customerIndex = this.customers.findIndex(c => c.id === customerId);
            if (customerIndex === -1) {
                throw new Error('Customer not found');
            }

            const customer = { 
                ...this.customers[customerIndex], 
                ...updates,
                updatedAt: new Date().toISOString()
            };

            await storage.save('customers', customer);
            this.customers[customerIndex] = customer;
            
            if (customer.status === 'active') {
                this.activeCustomers.set(customerId, customer);
            } else {
                this.activeCustomers.delete(customerId);
            }
            
            this.renderCustomers();
            this.updateDashboardStats();
            
            return customer;
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }

    // Delete customer
    async deleteCustomer(customerId) {
        try {
            await storage.delete('customers', customerId);
            this.customers = this.customers.filter(c => c.id !== customerId);
            this.activeCustomers.delete(customerId);
            
            this.renderCustomers();
            this.updateDashboardStats();
            this.addActivity(`تم حذف عميل`);
        } catch (error) {
            console.error('Error deleting customer:', error);
            throw error;
        }
    }

    // Start session for customer
    async startSession(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        await this.updateCustomer(customerId, {
            status: 'active',
            entryTime: new Date().toISOString(),
            exitTime: null,
            totalTime: 0,
            totalCost: 0,
            purchases: []
        });

        this.addActivity(`بدأ جلسة: ${customer.name}`);
    }

    // End session for customer
    async endSession(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.status !== 'active') return;

        const entryTime = new Date(customer.entryTime);
        const exitTime = new Date();
        const totalMinutes = Math.floor((exitTime - entryTime) / (1000 * 60));
        
        // Calculate time cost
        const hourlyRate = await this.getHourlyRate();
        const timeCost = (totalMinutes / 60) * hourlyRate;
        
        // Calculate total cost including purchases
        const purchasesCost = customer.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const totalCost = timeCost + purchasesCost;

        await this.updateCustomer(customerId, {
            status: 'completed',
            exitTime: exitTime.toISOString(),
            totalTime: totalMinutes,
            totalCost: totalCost
        });

        this.addActivity(`انتهت جلسة: ${customer.name} - ${totalMinutes} دقيقة`);
        
        // Generate invoice
        await this.generateInvoice(customerId);
        
        return { totalMinutes, totalCost };
    }

    // Add purchase to customer
    async addPurchase(customerId, productId, quantity = 1) {
        try {
            const customer = this.customers.find(c => c.id === customerId);
            const product = await storage.get('products', productId);
            
            if (!customer || !product) return;

            const purchase = {
                id: storage.generateId(),
                productId: productId,
                productName: product.name,
                productCode: product.code,
                price: product.price,
                quantity: quantity,
                total: product.price * quantity,
                timestamp: new Date().toISOString()
            };

            const purchases = [...customer.purchases, purchase];
            const purchasesCost = purchases.reduce((sum, p) => sum + p.total, 0);
            
            // Calculate time cost
            const entryTime = new Date(customer.entryTime);
            const currentTime = new Date();
            const totalMinutes = Math.floor((currentTime - entryTime) / (1000 * 60));
            const hourlyRate = await this.getHourlyRate();
            const timeCost = (totalMinutes / 60) * hourlyRate;
            
            await this.updateCustomer(customerId, {
                purchases: purchases,
                totalCost: timeCost + purchasesCost,
                totalTime: totalMinutes
            });

            this.addActivity(`إضافة مشتريات: ${product.name} للعميل ${customer.name}`);
            
            return purchase;
        } catch (error) {
            console.error('Error adding purchase:', error);
            throw error;
        }
    }

    // Remove purchase from customer
    async removePurchase(customerId, purchaseId) {
        try {
            const customer = this.customers.find(c => c.id === customerId);
            if (!customer) return;

            const purchases = customer.purchases.filter(p => p.id !== purchaseId);
            const purchasesCost = purchases.reduce((sum, p) => sum + p.total, 0);
            
            // Recalculate total cost
            const entryTime = new Date(customer.entryTime);
            const currentTime = new Date();
            const totalMinutes = Math.floor((currentTime - entryTime) / (1000 * 60));
            const hourlyRate = await this.getHourlyRate();
            const timeCost = (totalMinutes / 60) * hourlyRate;
            
            await this.updateCustomer(customerId, {
                purchases: purchases,
                totalCost: timeCost + purchasesCost
            });

            this.addActivity(`حذف مشتريات للعميل ${customer.name}`);
        } catch (error) {
            console.error('Error removing purchase:', error);
            throw error;
        }
    }

    // Generate invoice for customer
    async generateInvoice(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const invoice = {
            id: storage.generateId(),
            customerId: customerId,
            customerName: customer.name,
            customerPhone: customer.phone,
            entryTime: customer.entryTime,
            exitTime: customer.exitTime,
            totalTime: customer.totalTime,
            purchases: customer.purchases,
            timeCost: customer.totalCost - customer.purchases.reduce((sum, p) => sum + p.total, 0),
            purchasesCost: customer.purchases.reduce((sum, p) => sum + p.total, 0),
            totalCost: customer.totalCost,
            createdAt: new Date().toISOString(),
            status: 'completed'
        };

        await storage.save('invoices', invoice);
        this.addActivity(`تم إنشاء فاتورة للعميل: ${customer.name}`);
        
        return invoice;
    }

    // Get hourly rate from settings
    async getHourlyRate() {
        try {
            const setting = await storage.get('settings', 'hourlyRate');
            return setting ? setting.value : 10; // Default 10 EGP per hour
        } catch (error) {
            return 10;
        }
    }

    // Get daily rate from settings
    async getDailyRate() {
        try {
            const setting = await storage.get('settings', 'dailyRate');
            return setting ? setting.value : 65; // Default 65 EGP per day
        } catch (error) {
            return 65;
        }
    }

    // Search customers
    async searchCustomers(searchTerm) {
        if (!searchTerm) {
            this.renderCustomers();
            return;
        }

        const results = await storage.search('customers', searchTerm, ['name', 'phone']);
        this.renderCustomers(results);
    }

    // Render customers
    renderCustomers(customersToRender = null) {
        const customersGrid = document.getElementById('customersGrid');
        if (!customersGrid) return;

        const customers = customersToRender || this.customers;
        
        if (customers.length === 0) {
            customersGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-3x"></i>
                    <h3>لا توجد عملاء</h3>
                    <p>ابدأ بإضافة عميل جديد</p>
                </div>
            `;
            return;
        }

        customersGrid.innerHTML = customers.map(customer => this.createCustomerCard(customer)).join('');
    }

    // Create customer card HTML
    createCustomerCard(customer) {
        const isActive = customer.status === 'active';
        const currentTime = isActive ? this.calculateCurrentTime(customer.entryTime) : customer.totalTime;
        const statusClass = isActive ? 'status-active' : 'status-inactive';
        const statusText = isActive ? 'يذاكر الآن' : 'انتهى';

        return `
            <div class="customer-card fade-in ${isActive ? 'active-customer' : ''}">
                <div class="card-header">
                    <div class="card-title">${customer.name}</div>
                    <div class="card-actions">
                        ${isActive ? `
                            <button class="btn btn-sm btn-warning" onclick="customerManager.createInvoiceForCustomer('${customer.id}')" title="إنشاء فاتورة">
                                <i class="fas fa-file-invoice"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="customerManager.endSession('${customer.id}')" title="إنهاء الجلسة">
                                <i class="fas fa-stop"></i>
                            </button>
                        ` : `
                            <button class="btn btn-sm btn-primary" onclick="customerManager.startSession('${customer.id}')" title="بدء جلسة جديدة">
                                <i class="fas fa-play"></i>
                            </button>
                        `}
                        <button class="btn btn-sm btn-info" onclick="customerManager.showCustomerDetails('${customer.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="customerManager.editCustomer('${customer.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="customerManager.confirmDelete('${customer.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                ${isActive ? `
                    <div class="timer-display">
                        <div class="timer-circle">
                            <div class="timer-time" id="timer-${customer.id}">
                                ${this.formatTimeForTimer(currentTime)}
                            </div>
                            <div class="timer-label">وقت المذاكرة</div>
                        </div>
                        <div class="timer-cost">
                            <span class="cost-amount" id="cost-${customer.id}">${customer.totalCost.toFixed(2)} جنيه</span>
                        </div>
                    </div>
                ` : ''}

                <div class="card-info">
                    <p><strong>الحالة:</strong> <span class="status-badge ${statusClass}">${statusText}</span></p>
                    <p><strong>نوع الخدمة:</strong> <span class="service-type">${customer.serviceType === 'daily' ? 'إيجار 24 ساعة' : 'بالساعة'}</span></p>
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    ${!isActive ? `<p><strong>إجمالي الوقت:</strong> ${this.formatTime(currentTime)}</p>` : ''}
                    ${!isActive ? `<p><strong>التكلفة النهائية:</strong> ${customer.totalCost.toFixed(2)} جنيه</p>` : ''}
                    ${customer.purchases.length > 0 ? `<p><strong>المشتريات:</strong> ${customer.purchases.length} عنصر</p>` : ''}
                    <p><strong>وقت الدخول:</strong> ${this.formatDateTime(customer.entryTime)}</p>
                    ${customer.exitTime ? `<p><strong>وقت الخروج:</strong> ${this.formatDateTime(customer.exitTime)}</p>` : ''}
                </div>

                ${isActive ? `
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="customerManager.showAddPurchaseModal('${customer.id}')">
                            <i class="fas fa-plus"></i> إضافة مشتريات
                        </button>
                        <button class="btn btn-sm btn-info" onclick="customerManager.editSessionTime('${customer.id}')">
                            <i class="fas fa-clock"></i> تعديل الوقت
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Calculate current time for active customer
    calculateCurrentTime(entryTime) {
        const entry = new Date(entryTime);
        const now = new Date();
        return Math.floor((now - entry) / (1000 * 60));
    }

    // Format time in minutes
    formatTime(minutes) {
        if (minutes < 60) {
            return `${minutes} دقيقة`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}:${remainingMinutes.toString().padStart(2, '0')} ساعة`;
    }

    // Format time for timer display
    formatTimeForTimer(minutes) {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        const seconds = 0; // We'll update this with real-time seconds

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${remainingMinutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${remainingMinutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // Create invoice for active customer without ending session
    async createInvoiceForCustomer(customerId) {
        try {
            const customer = this.customers.find(c => c.id === customerId);
            if (!customer || customer.status !== 'active') {
                alert('العميل غير نشط');
                return;
            }

            // Calculate current time and cost
            const entryTime = new Date(customer.entryTime);
            const currentTime = new Date();
            const totalMinutes = Math.floor((currentTime - entryTime) / (1000 * 60));

            // Calculate time cost
            const hourlyRate = await this.getHourlyRate();
            const timeCost = (totalMinutes / 60) * hourlyRate;

            // Calculate total cost including purchases
            const purchasesCost = customer.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
            const totalCost = timeCost + purchasesCost;

            // Update customer with current costs (but don't end session)
            await this.updateCustomer(customerId, {
                totalTime: totalMinutes,
                totalCost: totalCost
            });

            // Generate invoice
            const invoice = {
                id: storage.generateId(),
                invoiceNumber: await this.generateInvoiceNumber(),
                customerId: customerId,
                customerName: customer.name,
                customerPhone: customer.phone,
                entryTime: customer.entryTime,
                exitTime: null, // Session still active
                totalTime: totalMinutes,
                purchases: [...customer.purchases],
                timeCost: timeCost,
                purchasesCost: purchasesCost,
                discount: 0,
                totalCost: totalCost,
                finalAmount: totalCost,
                paymentMethod: 'نقدي',
                notes: 'فاتورة جزئية - الجلسة مازالت نشطة',
                status: 'partial', // Mark as partial invoice
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await storage.save('invoices', invoice);
            this.addActivity(`تم إنشاء فاتورة جزئية للعميل: ${customer.name}`);

            // Show invoice and ask if user wants to print
            if (confirm('تم إنشاء الفاتورة بنجاح. هل تريد طباعتها؟')) {
                this.printInvoice(invoice);
            }

            return invoice;
        } catch (error) {
            console.error('Error creating invoice:', error);
            alert('حدث خطأ أثناء إنشاء الفاتورة');
        }
    }

    // Generate invoice number
    async generateInvoiceNumber() {
        const today = new Date();
        const datePrefix = today.getFullYear().toString().substr(-2) +
                          (today.getMonth() + 1).toString().padStart(2, '0') +
                          today.getDate().toString().padStart(2, '0');

        // Get today's invoices count
        const allInvoices = await storage.getAll('invoices');
        const todayInvoices = allInvoices.filter(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            return invoiceDate.toDateString() === today.toDateString();
        });

        const sequence = (todayInvoices.length + 1).toString().padStart(3, '0');
        return `INV-${datePrefix}-${sequence}`;
    }

    // Print invoice
    printInvoice(invoice) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.generatePrintableInvoice(invoice));
        printWindow.document.close();
        printWindow.print();
    }

    // Generate printable invoice HTML
    generatePrintableInvoice(invoice) {
        const placeName = 'مكان المذاكرة'; // Get from settings

        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة رقم ${invoice.invoiceNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .invoice-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 10px; }
                    .invoice-details { margin-bottom: 20px; }
                    .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .invoice-table th, .invoice-table td { border: 1px solid #000; padding: 8px; text-align: right; }
                    .invoice-table th { background-color: #f0f0f0; }
                    .total-row { font-weight: bold; font-size: 1.2em; }
                    .invoice-footer { text-align: center; margin-top: 30px; font-size: 0.9em; }
                    .partial-notice { background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>${placeName}</h1>
                    <h2>فاتورة رقم: ${invoice.invoiceNumber}</h2>
                    <p>التاريخ: ${this.formatDateTime(invoice.createdAt)}</p>
                </div>

                ${invoice.status === 'partial' ? `
                    <div class="partial-notice">
                        <strong>ملاحظة:</strong> هذه فاتورة جزئية - العميل مازال يذاكر
                    </div>
                ` : ''}

                <div class="invoice-details">
                    <p><strong>العميل:</strong> ${invoice.customerName}</p>
                    ${invoice.customerPhone ? `<p><strong>الهاتف:</strong> ${invoice.customerPhone}</p>` : ''}
                    <p><strong>وقت الدخول:</strong> ${this.formatDateTime(invoice.entryTime)}</p>
                    ${invoice.exitTime ? `<p><strong>وقت الخروج:</strong> ${this.formatDateTime(invoice.exitTime)}</p>` : '<p><strong>وقت الخروج:</strong> لم ينته بعد</p>'}
                    <p><strong>وقت المذاكرة:</strong> ${this.formatTime(invoice.totalTime)}</p>
                </div>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>وقت المذاكرة (${this.formatTime(invoice.totalTime)})</td>
                            <td>1</td>
                            <td>${invoice.timeCost.toFixed(2)} جنيه</td>
                            <td>${invoice.timeCost.toFixed(2)} جنيه</td>
                        </tr>
                        ${invoice.purchases.map(purchase => `
                            <tr>
                                <td>${purchase.productName}</td>
                                <td>${purchase.quantity}</td>
                                <td>${purchase.price.toFixed(2)} جنيه</td>
                                <td>${purchase.total.toFixed(2)} جنيه</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="3"><strong>المجموع النهائي</strong></td>
                            <td><strong>${invoice.finalAmount.toFixed(2)} جنيه</strong></td>
                        </tr>
                    </tbody>
                </table>

                <div class="invoice-footer">
                    <p>شكراً لزيارتكم - نتمنى لكم مذاكرة مثمرة</p>
                    <p>تم الطباعة في: ${new Date().toLocaleString('ar-EG')}</p>
                </div>
            </body>
            </html>
        `;
    }

    // Edit session time manually
    async editSessionTime(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.status !== 'active') return;

        const currentMinutes = this.calculateCurrentTime(customer.entryTime);
        const newTime = prompt(`الوقت الحالي: ${this.formatTime(currentMinutes)}\nأدخل الوقت الجديد بالدقائق:`, currentMinutes);

        if (newTime === null || isNaN(newTime) || newTime < 0) return;

        const newMinutes = parseInt(newTime);
        const now = new Date();
        const newEntryTime = new Date(now.getTime() - (newMinutes * 60 * 1000));

        await this.updateCustomer(customerId, {
            entryTime: newEntryTime.toISOString()
        });

        this.addActivity(`تم تعديل وقت العميل ${customer.name} إلى ${this.formatTime(newMinutes)}`);
    }

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG');
    }

    // Setup event listeners
    setupEventListeners() {
        // Customer search
        const searchInput = document.getElementById('customerSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchCustomers(e.target.value);
            });
        }

        // Update active customers time every second for real-time timers
        setInterval(() => {
            if (this.activeCustomers.size > 0) {
                this.updateActiveCustomerTimers();
            }
        }, 1000); // Update every second

        // Update full customer display every minute
        setInterval(() => {
            if (this.activeCustomers.size > 0) {
                this.renderCustomers();
                this.updateDashboardStats();
            }
        }, 60000); // Update every minute
    }

    // Update active customer timers in real-time
    updateActiveCustomerTimers() {
        this.activeCustomers.forEach(customer => {
            const timerElement = document.getElementById(`timer-${customer.id}`);
            const costElement = document.getElementById(`cost-${customer.id}`);

            if (timerElement) {
                const entryTime = new Date(customer.entryTime);
                const now = new Date();
                const totalSeconds = Math.floor((now - entryTime) / 1000);
                const totalMinutes = Math.floor(totalSeconds / 60);
                const hours = Math.floor(totalMinutes / 60);
                const minutes = totalMinutes % 60;
                const seconds = totalSeconds % 60;

                let timeDisplay;
                if (hours > 0) {
                    timeDisplay = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    timeDisplay = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }

                timerElement.textContent = timeDisplay;

                // Update cost in real-time
                if (costElement) {
                    this.updateCustomerCostRealTime(customer.id, totalMinutes);
                }
            }
        });
    }

    // Update customer cost in real-time
    async updateCustomerCostRealTime(customerId, totalMinutes) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        let timeCost = 0;
        if (customer.serviceType === 'daily') {
            // إيجار 24 ساعة
            const dailyRate = await this.getDailyRate();
            timeCost = dailyRate;
        } else {
            // بالساعة
            const hourlyRate = await this.getHourlyRate();
            timeCost = (totalMinutes / 60) * hourlyRate;
        }

        const purchasesCost = customer.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const totalCost = timeCost + purchasesCost;

        const costElement = document.getElementById(`cost-${customerId}`);
        if (costElement) {
            costElement.textContent = `${totalCost.toFixed(2)} جنيه`;
        }

        // Update customer object silently (without re-rendering)
        const customerIndex = this.customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            this.customers[customerIndex].totalTime = totalMinutes;
            this.customers[customerIndex].totalCost = totalCost;
        }
    }

    // Update dashboard statistics
    updateDashboardStats() {
        const activeCustomersElement = document.getElementById('activeCustomers');
        if (activeCustomersElement) {
            activeCustomersElement.textContent = this.activeCustomers.size;
        }
    }

    // Add activity to recent activity list
    addActivity(message) {
        if (window.activityManager) {
            window.activityManager.addActivity(message);
        }
    }

    // Show customer details modal
    showCustomerDetails(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const modalHTML = `
            <div class="modal large-modal">
                <div class="modal-header">
                    <h3>تفاصيل العميل: ${customer.name}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="customer-details-view">
                        <div class="customer-info">
                            <h4>البيانات الأساسية</h4>
                            <p><strong>الاسم:</strong> ${customer.name}</p>
                            ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                            ${customer.notes ? `<p><strong>الملاحظات:</strong> ${customer.notes}</p>` : ''}
                            <p><strong>الحالة:</strong> ${customer.status === 'active' ? 'نشط' : 'مكتمل'}</p>
                            <p><strong>تاريخ الإنشاء:</strong> ${this.formatDateTime(customer.createdAt)}</p>
                        </div>

                        <div class="session-info">
                            <h4>بيانات الجلسة</h4>
                            <p><strong>وقت الدخول:</strong> ${this.formatDateTime(customer.entryTime)}</p>
                            ${customer.exitTime ? `<p><strong>وقت الخروج:</strong> ${this.formatDateTime(customer.exitTime)}</p>` : ''}
                            <p><strong>إجمالي الوقت:</strong> ${this.formatTime(customer.totalTime)} دقيقة</p>
                            <p><strong>التكلفة الإجمالية:</strong> ${customer.totalCost.toFixed(2)} جنيه</p>
                        </div>

                        ${customer.purchases.length > 0 ? `
                            <div class="purchases-info">
                                <h4>المشتريات (${customer.purchases.length})</h4>
                                <table class="purchases-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>المجموع</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${customer.purchases.map(purchase => `
                                            <tr>
                                                <td>${purchase.productName}</td>
                                                <td>${purchase.quantity}</td>
                                                <td>${purchase.price.toFixed(2)} جنيه</td>
                                                <td>${purchase.total.toFixed(2)} جنيه</td>
                                                <td>${this.formatDateTime(purchase.timestamp)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : '<p>لا توجد مشتريات</p>'}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Edit customer
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // Fill form with customer data
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerNotes').value = customer.notes || '';

        // Store customer ID for update
        document.getElementById('customerForm').dataset.customerId = customerId;

        // Show modal
        showModal('addCustomerModal');
    }

    // Confirm delete customer
    confirmDelete(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
            this.deleteCustomer(customerId);
        }
    }

    // Show add purchase modal
    showAddPurchaseModal(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const products = productManager.getActiveProducts();

        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>إضافة مشتريات - ${customer.name}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="products-selection">
                        <div class="search-bar">
                            <input type="text" id="purchaseProductSearch" placeholder="البحث عن منتج أو كود..." class="search-input">
                        </div>
                        <div class="products-grid" id="purchaseProductsGrid">
                            ${products.map(product => `
                                <div class="product-option" onclick="customerManager.addPurchaseToCustomer('${customerId}', '${product.id}')">
                                    <div class="product-info">
                                        <h4>${product.name}</h4>
                                        <p>الكود: ${product.code}</p>
                                        <p class="product-price">${product.price.toFixed(2)} جنيه</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();

        // Setup search for products
        document.getElementById('purchaseProductSearch').addEventListener('input', (e) => {
            this.filterPurchaseProducts(e.target.value, products);
        });
    }

    // Filter products in purchase modal
    filterPurchaseProducts(searchTerm, products) {
        const filtered = products.filter(product =>
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.code.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const grid = document.getElementById('purchaseProductsGrid');
        if (grid) {
            grid.innerHTML = filtered.map(product => `
                <div class="product-option" onclick="customerManager.addPurchaseToCustomer('${customer.id}', '${product.id}')">
                    <div class="product-info">
                        <h4>${product.name}</h4>
                        <p>الكود: ${product.code}</p>
                        <p class="product-price">${product.price.toFixed(2)} جنيه</p>
                    </div>
                </div>
            `).join('');
        }
    }

    // Add purchase to customer from modal
    async addPurchaseToCustomer(customerId, productId) {
        await this.addPurchase(customerId, productId);
        closeModal();
    }
}

// Initialize customer manager
const customerManager = new CustomerManager();
