<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة سكون</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/print.css" media="print">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-book-open"></i> نظام إدارة سكون</h1>
                <div class="header-controls">
                    <button id="themeToggle" class="btn btn-icon" title="تبديل الوضع الليلي">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="safeExitBtn" class="btn btn-icon btn-danger" title="إغلاق البرنامج بأمان" onclick="app.safeExit()">
                        <i class="fas fa-power-off"></i>
                    </button>
                    <div class="user-info">
                        <span id="currentUser">المدير</span>
                        <button id="logoutBtn" class="btn btn-secondary">تسجيل خروج</button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul class="nav-list">
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>
                <li><a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i> العملاء
                </a></li>
                <li><a href="#products" class="nav-link" data-section="products">
                    <i class="fas fa-shopping-cart"></i> المنتجات
                </a></li>
                <li><a href="#invoices" class="nav-link" data-section="invoices">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a></li>
                <li><a href="#employees" class="nav-link" data-section="employees">
                    <i class="fas fa-users"></i> الموظفين
                </a></li>
                <li><a href="#shifts" class="nav-link" data-section="shifts">
                    <i class="fas fa-clock"></i> الشيفتات
                </a></li>
                <li><a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a></li>
                <li><a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i> الإعدادات
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Dashboard Section -->
            <section id="dashboard" class="section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeCustomers">0</h3>
                            <p>العملاء النشطين</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayRevenue">0 جنيه</h3>
                            <p>إيرادات اليوم</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="currentShift">مغلق</h3>
                            <p>الشيفت الحالي</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayInvoices">0</h3>
                            <p>فواتير اليوم</p>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>الإجراءات السريعة</h3>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-user-plus"></i> إضافة عميل جديد
                        </button>
                        <button class="btn btn-success" onclick="openShift()">
                            <i class="fas fa-play"></i> فتح شيفت
                        </button>
                        <button class="btn btn-warning" onclick="closeShift()">
                            <i class="fas fa-stop"></i> إغلاق شيفت
                        </button>
                        <button class="btn btn-info" onclick="showSection('reports')">
                            <i class="fas fa-chart-line"></i> عرض التقارير
                        </button>
                    </div>
                </div>

                <div class="recent-activity">
                    <h3>النشاط الأخير</h3>
                    <div id="recentActivityList" class="activity-list">
                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                        <i class="fas fa-plus"></i> إضافة عميل
                    </button>
                </div>

                <div class="search-bar">
                    <input type="text" id="customerSearch" placeholder="البحث عن عميل..." class="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="customers-grid" id="customersGrid">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
            </section>

            <!-- Employees Section -->
            <section id="employees" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> إدارة الموظفين</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="employeeManager.showAddEmployeeModal()">
                            <i class="fas fa-user-plus"></i> إضافة موظف
                        </button>
                        <button class="btn btn-warning" onclick="employeeManager.resetEmployees()" title="إعادة تعيين الموظفين الافتراضيين">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <div class="section-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="employeeSearch" placeholder="البحث في الموظفين...">
                    </div>
                    <div class="filter-controls">
                        <select id="employeeStatusFilter">
                            <option value="all">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value" id="totalEmployees">0</div>
                            <div class="stat-label">إجمالي الموظفين</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value" id="activeEmployees">0</div>
                            <div class="stat-label">موظفين نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value" id="inactiveEmployees">0</div>
                            <div class="stat-label">موظفين غير نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value" id="activeShiftsCount">0</div>
                            <div class="stat-label">شيفتات نشطة</div>
                        </div>
                    </div>
                </div>

                <div id="employeesContainer" class="content-container">
                    <!-- Employees will be loaded here -->
                </div>

                <div id="employeesPagination" class="pagination-container">
                    <!-- Pagination will be loaded here -->
                </div>
            </section>

            <!-- Products Section -->
            <section id="products" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-coffee"></i> إدارة المنتجات</h2>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>

                <div class="search-bar">
                    <input type="text" id="productSearch" placeholder="البحث عن منتج..." class="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="filter-bar">
                    <select id="categoryFilter" class="filter-input">
                        <option value="all">جميع الفئات</option>
                        <option value="مشروبات">مشروبات</option>
                        <option value="وجبات خفيفة">وجبات خفيفة</option>
                        <option value="حلويات">حلويات</option>
                        <option value="قرطاسية">قرطاسية</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>

                <div class="products-grid" id="productsGrid">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
            </section>

            <!-- Invoices Section -->
            <section id="invoices" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-file-invoice"></i> الفواتير</h2>
                </div>

                <div class="search-bar">
                    <input type="text" id="invoiceSearch" placeholder="البحث في الفواتير..." class="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="invoices-list" id="invoicesList">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
            </section>

            <!-- Shifts Section -->
            <section id="shifts" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-clock"></i> إدارة الشيفتات</h2>
                </div>

                <div class="shift-controls">
                    <div class="current-shift-info" id="currentShiftInfo">
                        <!-- معلومات الشيفت الحالي -->
                    </div>
                </div>

                <div class="shifts-history" id="shiftsHistory">
                    <!-- تاريخ الشيفتات -->
                </div>
            </section>

            <!-- Reports Section -->
            <section id="reports" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <h3>تقرير يومي</h3>
                        <div id="dailyReport"></div>
                    </div>
                    
                    <div class="report-card">
                        <h3>تقرير أسبوعي</h3>
                        <div id="weeklyReport"></div>
                    </div>
                    
                    <div class="report-card">
                        <h3>تقرير شهري</h3>
                        <div id="monthlyReport"></div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                </div>

                <div class="settings-grid">
                    <div class="setting-card">
                        <h3>إعدادات عامة</h3>
                        <div class="setting-item">
                            <label>اسم المكان:</label>
                            <input type="text" id="placeName" value="سكون">
                        </div>
                        <div class="setting-item">
                            <label>سعر الساعة (جنيه):</label>
                            <input type="number" id="hourlyRate" value="10" step="0.01" min="0">
                        </div>
                        <div class="setting-item">
                            <label>سعر إيجار 24 ساعة (جنيه):</label>
                            <input type="number" id="dailyRate" value="65" step="0.01" min="0">
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-primary" onclick="app.saveSettings()">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>

                    <div class="setting-card">
                        <h3>النسخ الاحتياطي</h3>
                        <button class="btn btn-success" onclick="exportData()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                        <button class="btn btn-warning" onclick="importData()">
                            <i class="fas fa-upload"></i> استيراد البيانات
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Modals -->
    <div id="modalOverlay" class="modal-overlay">
        <!-- Add Customer Modal -->
        <div id="addCustomerModal" class="modal">
            <div class="modal-header">
                <h3>إضافة عميل جديد</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="form-group">
                        <label>اسم العميل:</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف:</label>
                        <input type="tel" id="customerPhone">
                    </div>
                    <div class="form-group">
                        <label>نوع الخدمة:</label>
                        <select id="serviceType" onchange="updateServiceInfo()">
                            <option value="hourly">بالساعة</option>
                            <option value="daily">إيجار 24 ساعة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <div id="serviceInfo" class="service-info">
                            <span id="serviceRate">10 جنيه/ساعة</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات:</label>
                        <textarea id="customerNotes"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="addCustomer()">إضافة</button>
            </div>
        </div>

        <!-- Add Product Modal -->
        <div id="addProductModal" class="modal">
            <div class="modal-header">
                <h3>إضافة منتج جديد</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-group">
                        <label>اسم المنتج:</label>
                        <input type="text" id="productName" required>
                    </div>
                    <div class="form-group">
                        <label>كود المنتج:</label>
                        <input type="text" id="productCode" required>
                    </div>
                    <div class="form-group">
                        <label>السعر (جنيه):</label>
                        <input type="number" id="productPrice" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>الفئة:</label>
                        <select id="productCategory">
                            <option value="مشروبات">مشروبات</option>
                            <option value="وجبات خفيفة">وجبات خفيفة</option>
                            <option value="حلويات">حلويات</option>
                            <option value="قرطاسية">قرطاسية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="addProduct()">إضافة</button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container">
        <!-- Notifications will be inserted here -->
    </div>

    <!-- Scripts -->
    <script src="js/storage.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/products.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/invoices.js"></script>
    <script src="js/shifts.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/advanced-features.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
