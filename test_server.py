#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السيرفر المحلي لنظام إدارة مكان المذاكرة
"""

import os
import sys
import time
import requests
import threading
import subprocess
from pathlib import Path

def test_python_installation():
    """اختبار تثبيت Python"""
    print("🔍 اختبار تثبيت Python...")
    
    try:
        version = sys.version_info
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} مثبت")
        
        if version.major < 3 or (version.major == 3 and version.minor < 6):
            print("⚠️  تحذير: يُنصح بـ Python 3.6 أو أحدث")
            return False
        
        return True
    except Exception as e:
        print(f"❌ خطأ في فحص Python: {e}")
        return False

def test_required_files():
    """اختبار وجود الملفات المطلوبة"""
    print("\n🔍 اختبار الملفات المطلوبة...")
    
    required_files = [
        "index.html",
        "server.py",
        "start_server.bat",
        "start_server.sh",
        "js/app.js",
        "js/storage.js",
        "css/styles.css"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ جميع الملفات موجودة")
    return True

def test_server_start():
    """اختبار تشغيل السيرفر"""
    print("\n🔍 اختبار تشغيل السيرفر...")
    
    try:
        # تشغيل السيرفر في خيط منفصل
        server_process = subprocess.Popen(
            [sys.executable, "server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # انتظار قليل للسيرفر ليبدأ
        time.sleep(3)
        
        # فحص إذا كان السيرفر يعمل
        if server_process.poll() is None:
            print("✅ السيرفر يعمل")
            
            # إيقاف السيرفر
            server_process.terminate()
            server_process.wait(timeout=5)
            print("✅ تم إيقاف السيرفر بنجاح")
            return True
        else:
            stdout, stderr = server_process.communicate()
            print(f"❌ فشل في تشغيل السيرفر")
            print(f"خطأ: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار السيرفر: {e}")
        return False

def test_backup_system():
    """اختبار نظام النسخ الاحتياطي"""
    print("\n🔍 اختبار نظام النسخ الاحتياطي...")
    
    try:
        # استيراد وظائف السيرفر
        sys.path.append('.')
        import server
        
        # إنشاء نظام النسخ الاحتياطي
        server.create_backup_system()
        
        # فحص إنشاء المجلد
        if os.path.exists("backups"):
            print("✅ تم إنشاء مجلد النسخ الاحتياطي")
            
            # فحص ملف التكوين
            config_file = "backups/config.json"
            if os.path.exists(config_file):
                print("✅ تم إنشاء ملف التكوين")
                return True
            else:
                print("❌ ملف التكوين غير موجود")
                return False
        else:
            print("❌ فشل في إنشاء مجلد النسخ الاحتياطي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النسخ الاحتياطي: {e}")
        return False

def test_port_availability():
    """اختبار توفر المنافذ"""
    print("\n🔍 اختبار توفر المنافذ...")
    
    try:
        import socket
        
        # اختبار المنافذ من 8000 إلى 8010
        available_ports = []
        for port in range(8000, 8011):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    available_ports.append(port)
            except OSError:
                pass
        
        if available_ports:
            print(f"✅ منافذ متاحة: {available_ports}")
            return True
        else:
            print("❌ لا توجد منافذ متاحة في النطاق 8000-8010")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المنافذ: {e}")
        return False

def test_browser_compatibility():
    """اختبار توافق المتصفح"""
    print("\n🔍 اختبار توافق المتصفح...")
    
    try:
        import webbrowser
        
        # محاولة فتح صفحة اختبار
        test_url = "data:text/html,<h1>اختبار المتصفح</h1><p>إذا رأيت هذه الرسالة، فالمتصفح يعمل بشكل صحيح</p>"
        
        # فتح المتصفح (لن يفتح فعلياً في الاختبار)
        print("✅ وحدة المتصفح متاحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المتصفح: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات النظام الشاملة")
    print("=" * 50)
    
    tests = [
        ("تثبيت Python", test_python_installation),
        ("الملفات المطلوبة", test_required_files),
        ("تشغيل السيرفر", test_server_start),
        ("نظام النسخ الاحتياطي", test_backup_system),
        ("توفر المنافذ", test_port_availability),
        ("توافق المتصفح", test_browser_compatibility)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبارات: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️  بعض الاختبارات فشلت. راجع الأخطاء أعلاه")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎓 اختبار نظام إدارة مكان المذاكرة")
    print("Study Place Management System Test")
    print("=" * 50)
    
    success = run_all_tests()
    
    if success:
        print("\n✅ النظام جاهز! يمكنك الآن تشغيل start_server.bat")
    else:
        print("\n❌ يرجى حل المشاكل المذكورة أعلاه قبل التشغيل")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
