// Invoice Management System
class InvoiceManager {
    constructor() {
        this.invoices = [];
        this.init();
    }

    async init() {
        await this.loadInvoices();
        this.renderInvoices();
        this.setupEventListeners();
    }

    // Load invoices from storage
    async loadInvoices() {
        try {
            this.invoices = await storage.getAll('invoices');

            // إذا لم توجد فواتير في IndexedDB، جرب localStorage
            if (this.invoices.length === 0) {
                this.invoices = this.loadFromLocalStorage();
            }

            // Sort by creation date (newest first)
            this.invoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            console.log(`تم تحميل ${this.invoices.length} فاتورة`);
        } catch (error) {
            console.error('Error loading invoices:', error);
            // محاولة الاستعادة من localStorage في حالة الخطأ
            this.invoices = this.loadFromLocalStorage();
        }
    }

    // Load invoices from localStorage as backup
    loadFromLocalStorage() {
        const invoices = [];
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('invoice_')) {
                    const invoiceData = localStorage.getItem(key);
                    if (invoiceData) {
                        const invoice = JSON.parse(invoiceData);
                        invoices.push(invoice);
                    }
                }
            }
            console.log(`تم استعادة ${invoices.length} فاتورة من localStorage`);
        } catch (error) {
            console.error('Error loading from localStorage:', error);
        }
        return invoices;
    }

    // Create new invoice
    async createInvoice(invoiceData) {
        try {
            const invoice = {
                id: storage.generateId(),
                invoiceNumber: await this.generateInvoiceNumber(),
                customerId: invoiceData.customerId,
                customerName: invoiceData.customerName,
                customerPhone: invoiceData.customerPhone || '',
                entryTime: invoiceData.entryTime,
                exitTime: invoiceData.exitTime,
                totalTime: invoiceData.totalTime,
                purchases: invoiceData.purchases || [],
                timeCost: invoiceData.timeCost || 0,
                purchasesCost: invoiceData.purchasesCost || 0,
                discount: invoiceData.discount || 0,
                totalCost: invoiceData.totalCost,
                finalAmount: invoiceData.totalCost - (invoiceData.discount || 0),
                paymentMethod: invoiceData.paymentMethod || 'نقدي',
                notes: invoiceData.notes || '',
                status: 'completed',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // محاولة الحفظ مع نظام احتياطي
            try {
                await storage.save('invoices', invoice);
                console.log('تم حفظ الفاتورة في IndexedDB');
            } catch (storageError) {
                console.error('خطأ في حفظ الفاتورة في IndexedDB:', storageError);
                // حفظ في localStorage كبديل
                localStorage.setItem(`invoice_${invoice.id}`, JSON.stringify(invoice));
                console.log('تم حفظ الفاتورة في localStorage كبديل');
            }

            this.invoices.unshift(invoice); // Add to beginning

            this.renderInvoices();
            this.updateDashboardStats();
            this.addActivity(`تم إنشاء فاتورة رقم: ${invoice.invoiceNumber}`);
            
            return invoice;
        } catch (error) {
            console.error('Error creating invoice:', error);
            throw error;
        }
    }

    // Generate invoice number
    async generateInvoiceNumber() {
        const today = new Date();
        const datePrefix = today.getFullYear().toString().substr(-2) + 
                          (today.getMonth() + 1).toString().padStart(2, '0') + 
                          today.getDate().toString().padStart(2, '0');
        
        // Get today's invoices count
        const todayInvoices = this.invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            return invoiceDate.toDateString() === today.toDateString();
        });
        
        const sequence = (todayInvoices.length + 1).toString().padStart(3, '0');
        return `INV-${datePrefix}-${sequence}`;
    }

    // Update invoice
    async updateInvoice(invoiceId, updates) {
        try {
            const invoiceIndex = this.invoices.findIndex(i => i.id === invoiceId);
            if (invoiceIndex === -1) {
                throw new Error('Invoice not found');
            }

            const invoice = { 
                ...this.invoices[invoiceIndex], 
                ...updates,
                updatedAt: new Date().toISOString()
            };

            await storage.save('invoices', invoice);
            this.invoices[invoiceIndex] = invoice;
            
            this.renderInvoices();
            this.addActivity(`تم تحديث الفاتورة رقم: ${invoice.invoiceNumber}`);
            
            return invoice;
        } catch (error) {
            console.error('Error updating invoice:', error);
            throw error;
        }
    }

    // Delete invoice
    async deleteInvoice(invoiceId) {
        try {
            const invoice = this.invoices.find(i => i.id === invoiceId);
            if (!invoice) return;

            await storage.delete('invoices', invoiceId);
            this.invoices = this.invoices.filter(i => i.id !== invoiceId);
            
            this.renderInvoices();
            this.updateDashboardStats();
            this.addActivity(`تم حذف الفاتورة رقم: ${invoice.invoiceNumber}`);
        } catch (error) {
            console.error('Error deleting invoice:', error);
            throw error;
        }
    }

    // Search invoices
    async searchInvoices(searchTerm) {
        if (!searchTerm) {
            this.renderInvoices();
            return;
        }

        const results = await storage.search('invoices', searchTerm, ['invoiceNumber', 'customerName', 'customerPhone']);
        this.renderInvoices(results);
    }

    // Filter invoices by date range
    filterByDateRange(startDate, endDate) {
        const filtered = this.invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            const start = startDate ? new Date(startDate) : new Date('1900-01-01');
            const end = endDate ? new Date(endDate) : new Date();
            
            return invoiceDate >= start && invoiceDate <= end;
        });
        
        this.renderInvoices(filtered);
    }

    // Get today's invoices
    getTodayInvoices() {
        const today = new Date();
        return this.invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            return invoiceDate.toDateString() === today.toDateString();
        });
    }

    // Get invoices by date range
    getInvoicesByDateRange(startDate, endDate) {
        return this.invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            return invoiceDate >= startDate && invoiceDate <= endDate;
        });
    }

    // Calculate total revenue
    calculateTotalRevenue(invoices = null) {
        const invoicesToCalculate = invoices || this.invoices;
        return invoicesToCalculate.reduce((total, invoice) => total + invoice.finalAmount, 0);
    }

    // Render invoices
    renderInvoices(invoicesToRender = null) {
        const invoicesList = document.getElementById('invoicesList');
        if (!invoicesList) return;

        const invoices = invoicesToRender || this.invoices;
        
        if (invoices.length === 0) {
            invoicesList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-file-invoice fa-3x"></i>
                    <h3>لا توجد فواتير</h3>
                    <p>ستظهر الفواتير هنا عند إنشائها</p>
                </div>
            `;
            return;
        }

        invoicesList.innerHTML = `
            <div class="invoices-header">
                <div class="invoices-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الفواتير:</span>
                        <span class="stat-value">${invoices.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">إجمالي المبلغ:</span>
                        <span class="stat-value">${this.calculateTotalRevenue(invoices).toFixed(2)} جنيه</span>
                    </div>
                </div>
                <div class="invoices-filters">
                    <input type="date" id="startDate" class="filter-input">
                    <input type="date" id="endDate" class="filter-input">
                    <button class="btn btn-primary" onclick="invoiceManager.applyDateFilter()">
                        <i class="fas fa-filter"></i> تطبيق
                    </button>
                    <button class="btn btn-secondary" onclick="invoiceManager.clearFilters()">
                        <i class="fas fa-times"></i> مسح
                    </button>
                </div>
            </div>
            <div class="invoices-grid">
                ${invoices.map(invoice => this.createInvoiceCard(invoice)).join('')}
            </div>
        `;
    }

    // Create invoice card HTML
    createInvoiceCard(invoice) {
        const invoiceDate = new Date(invoice.createdAt);
        const hasDiscount = invoice.discount > 0;

        return `
            <div class="invoice-card fade-in">
                <div class="card-header">
                    <div class="invoice-number">
                        <strong>فاتورة رقم: ${invoice.invoiceNumber}</strong>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="invoiceManager.printInvoice('${invoice.id}')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="invoiceManager.viewInvoice('${invoice.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="invoiceManager.editInvoice('${invoice.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoiceManager.confirmDelete('${invoice.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="invoice-info">
                    <div class="info-row">
                        <span class="info-label">العميل:</span>
                        <span class="info-value">${invoice.customerName}</span>
                    </div>
                    ${invoice.customerPhone ? `
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">${invoice.customerPhone}</span>
                        </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">التاريخ:</span>
                        <span class="info-value">${this.formatDateTime(invoice.createdAt)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الوقت:</span>
                        <span class="info-value">${this.formatTime(invoice.totalTime)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تكلفة الوقت:</span>
                        <span class="info-value">${invoice.timeCost.toFixed(2)} جنيه</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">المشتريات:</span>
                        <span class="info-value">${invoice.purchasesCost.toFixed(2)} جنيه</span>
                    </div>
                    ${hasDiscount ? `
                        <div class="info-row">
                            <span class="info-label">الخصم:</span>
                            <span class="info-value discount">-${invoice.discount.toFixed(2)} جنيه</span>
                        </div>
                    ` : ''}
                    <div class="info-row total-row">
                        <span class="info-label">المجموع النهائي:</span>
                        <span class="info-value total">${invoice.finalAmount.toFixed(2)} جنيه</span>
                    </div>
                </div>
                
                ${invoice.purchases.length > 0 ? `
                    <div class="invoice-purchases">
                        <h4>المشتريات (${invoice.purchases.length}):</h4>
                        <div class="purchases-list">
                            ${invoice.purchases.map(purchase => `
                                <div class="purchase-item">
                                    <span>${purchase.productName}</span>
                                    <span>${purchase.quantity} × ${purchase.price.toFixed(2)} = ${purchase.total.toFixed(2)} جنيه</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Print invoice
    printInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.generatePrintableInvoice(invoice));
        printWindow.document.close();
        printWindow.print();
    }

    // Generate printable invoice HTML
    generatePrintableInvoice(invoice) {
        const cafeName = 'مقهى الأصدقاء'; // Get from settings
        
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة رقم ${invoice.invoiceNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .invoice-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 10px; }
                    .invoice-details { margin-bottom: 20px; }
                    .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .invoice-table th, .invoice-table td { border: 1px solid #000; padding: 8px; text-align: right; }
                    .invoice-table th { background-color: #f0f0f0; }
                    .total-row { font-weight: bold; font-size: 1.2em; }
                    .invoice-footer { text-align: center; margin-top: 30px; font-size: 0.9em; }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>${cafeName}</h1>
                    <h2>فاتورة رقم: ${invoice.invoiceNumber}</h2>
                    <p>التاريخ: ${this.formatDateTime(invoice.createdAt)}</p>
                </div>
                
                <div class="invoice-details">
                    <p><strong>العميل:</strong> ${invoice.customerName}</p>
                    ${invoice.customerPhone ? `<p><strong>الهاتف:</strong> ${invoice.customerPhone}</p>` : ''}
                    <p><strong>وقت الدخول:</strong> ${this.formatDateTime(invoice.entryTime)}</p>
                    <p><strong>وقت الخروج:</strong> ${this.formatDateTime(invoice.exitTime)}</p>
                    <p><strong>إجمالي الوقت:</strong> ${this.formatTime(invoice.totalTime)}</p>
                </div>
                
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>وقت الجلسة (${this.formatTime(invoice.totalTime)})</td>
                            <td>1</td>
                            <td>${invoice.timeCost.toFixed(2)} جنيه</td>
                            <td>${invoice.timeCost.toFixed(2)} جنيه</td>
                        </tr>
                        ${invoice.purchases.map(purchase => `
                            <tr>
                                <td>${purchase.productName}</td>
                                <td>${purchase.quantity}</td>
                                <td>${purchase.price.toFixed(2)} جنيه</td>
                                <td>${purchase.total.toFixed(2)} جنيه</td>
                            </tr>
                        `).join('')}
                        <tr>
                            <td colspan="3"><strong>المجموع الفرعي</strong></td>
                            <td><strong>${invoice.totalCost.toFixed(2)} جنيه</strong></td>
                        </tr>
                        ${invoice.discount > 0 ? `
                            <tr>
                                <td colspan="3"><strong>الخصم</strong></td>
                                <td><strong>-${invoice.discount.toFixed(2)} جنيه</strong></td>
                            </tr>
                        ` : ''}
                        <tr class="total-row">
                            <td colspan="3"><strong>المجموع النهائي</strong></td>
                            <td><strong>${invoice.finalAmount.toFixed(2)} جنيه</strong></td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="invoice-footer">
                    <p>شكراً لزيارتكم - نتطلع لرؤيتكم مرة أخرى</p>
                    <p>تم الطباعة في: ${new Date().toLocaleString('ar-EG')}</p>
                </div>
            </body>
            </html>
        `;
    }

    // View invoice details
    viewInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        // Show invoice details modal
        this.showInvoiceDetailsModal(invoice);
    }

    // Show invoice details modal
    showInvoiceDetailsModal(invoice) {
        const modalHTML = `
            <div class="modal large-modal">
                <div class="modal-header">
                    <h3>تفاصيل الفاتورة رقم: ${invoice.invoiceNumber}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="invoice-details-view">
                        ${this.generateInvoiceDetailsHTML(invoice)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="invoiceManager.printInvoice('${invoice.id}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalOverlay').innerHTML = modalHTML;
        showModal();
    }

    // Generate invoice details HTML
    generateInvoiceDetailsHTML(invoice) {
        return `
            <div class="invoice-preview">
                <div class="invoice-header-preview">
                    <h2>فاتورة رقم: ${invoice.invoiceNumber}</h2>
                    <p>التاريخ: ${this.formatDateTime(invoice.createdAt)}</p>
                </div>
                
                <div class="customer-info">
                    <h3>بيانات العميل</h3>
                    <p><strong>الاسم:</strong> ${invoice.customerName}</p>
                    ${invoice.customerPhone ? `<p><strong>الهاتف:</strong> ${invoice.customerPhone}</p>` : ''}
                </div>
                
                <div class="session-info">
                    <h3>بيانات الجلسة</h3>
                    <p><strong>وقت الدخول:</strong> ${this.formatDateTime(invoice.entryTime)}</p>
                    <p><strong>وقت الخروج:</strong> ${this.formatDateTime(invoice.exitTime)}</p>
                    <p><strong>إجمالي الوقت:</strong> ${this.formatTime(invoice.totalTime)}</p>
                    <p><strong>تكلفة الوقت:</strong> ${invoice.timeCost.toFixed(2)} جنيه</p>
                </div>
                
                ${invoice.purchases.length > 0 ? `
                    <div class="purchases-info">
                        <h3>المشتريات</h3>
                        <table class="purchases-table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${invoice.purchases.map(purchase => `
                                    <tr>
                                        <td>${purchase.productName}</td>
                                        <td>${purchase.quantity}</td>
                                        <td>${purchase.price.toFixed(2)} جنيه</td>
                                        <td>${purchase.total.toFixed(2)} جنيه</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}
                
                <div class="invoice-summary">
                    <div class="summary-row">
                        <span>تكلفة الوقت:</span>
                        <span>${invoice.timeCost.toFixed(2)} جنيه</span>
                    </div>
                    <div class="summary-row">
                        <span>تكلفة المشتريات:</span>
                        <span>${invoice.purchasesCost.toFixed(2)} جنيه</span>
                    </div>
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span>${invoice.totalCost.toFixed(2)} جنيه</span>
                    </div>
                    ${invoice.discount > 0 ? `
                        <div class="summary-row discount-row">
                            <span>الخصم:</span>
                            <span>-${invoice.discount.toFixed(2)} جنيه</span>
                        </div>
                    ` : ''}
                    <div class="summary-row total-row">
                        <span><strong>المجموع النهائي:</strong></span>
                        <span><strong>${invoice.finalAmount.toFixed(2)} جنيه</strong></span>
                    </div>
                </div>
            </div>
        `;
    }

    // Setup event listeners
    setupEventListeners() {
        // Invoice search
        const searchInput = document.getElementById('invoiceSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchInvoices(e.target.value);
            });
        }
    }

    // Apply date filter
    applyDateFilter() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (startDate || endDate) {
            this.filterByDateRange(startDate, endDate);
        }
    }

    // Clear filters
    clearFilters() {
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        this.renderInvoices();
    }

    // Edit invoice
    editInvoice(invoiceId) {
        // Implementation for editing invoice
        console.log('Edit invoice:', invoiceId);
    }

    // Confirm delete invoice
    confirmDelete(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        if (confirm(`هل أنت متأكد من حذف الفاتورة رقم "${invoice.invoiceNumber}"؟`)) {
            this.deleteInvoice(invoiceId);
        }
    }

    // Format time in minutes
    formatTime(minutes) {
        if (minutes < 60) {
            return `${minutes} دقيقة`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}:${remainingMinutes.toString().padStart(2, '0')} ساعة`;
    }

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG');
    }

    // Update dashboard statistics
    updateDashboardStats() {
        const todayInvoices = this.getTodayInvoices();
        const todayRevenue = this.calculateTotalRevenue(todayInvoices);

        const todayRevenueElement = document.getElementById('todayRevenue');
        const todayInvoicesElement = document.getElementById('todayInvoices');

        if (todayRevenueElement) {
            todayRevenueElement.textContent = `${todayRevenue.toFixed(2)} جنيه`;
        }

        if (todayInvoicesElement) {
            todayInvoicesElement.textContent = todayInvoices.length;
        }
    }

    // Add activity to recent activity list
    addActivity(message) {
        if (window.activityManager) {
            window.activityManager.addActivity(message);
        }
    }
}

// Initialize invoice manager
const invoiceManager = new InvoiceManager();
