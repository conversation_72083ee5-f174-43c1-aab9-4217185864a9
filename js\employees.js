class EmployeeManager {
    constructor() {
        this.employees = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.filterStatus = 'all';
    }

    async init() {
        await this.loadEmployees();
        this.renderEmployees();
        this.setupEventListeners();
        this.updateStats();
    }

    // Load employees from storage
    async loadEmployees() {
        try {
            this.employees = await storage.getAll('employees');
            
            // إضافة موظف افتراضي إذا لم يوجد موظفين
            if (this.employees.length === 0) {
                await this.createDefaultEmployee();
            }
            
            // Sort by creation date (newest first)
            this.employees.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (error) {
            console.error('Error loading employees:', error);
            this.employees = [];
        }
    }

    // Create default employee
    async createDefaultEmployee() {
        const defaultEmployee = {
            id: storage.generateId(),
            employeeCode: 'EMP-001',
            name: 'المدير العام',
            phone: '',
            email: '',
            position: 'مدير',
            department: 'الإدارة',
            hireDate: new Date().toISOString().split('T')[0],
            salary: 0,
            status: 'active',
            permissions: {
                canOpenShift: true,
                canCloseShift: true,
                canAddCustomers: true,
                canEditPrices: true,
                canViewReports: true,
                canManageEmployees: true,
                canManageProducts: true,
                canDeleteData: true
            },
            workSchedule: {
                sunday: { enabled: true, start: '09:00', end: '17:00' },
                monday: { enabled: true, start: '09:00', end: '17:00' },
                tuesday: { enabled: true, start: '09:00', end: '17:00' },
                wednesday: { enabled: true, start: '09:00', end: '17:00' },
                thursday: { enabled: true, start: '09:00', end: '17:00' },
                friday: { enabled: false, start: '09:00', end: '17:00' },
                saturday: { enabled: false, start: '09:00', end: '17:00' }
            },
            notes: 'الموظف الافتراضي للنظام',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        await storage.save('employees', defaultEmployee);
        this.employees.push(defaultEmployee);
    }

    // Generate employee code
    async generateEmployeeCode() {
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        
        // Get all employees to find the next sequence number
        const allEmployees = await storage.getAll('employees');
        const thisYearEmployees = allEmployees.filter(emp => {
            const empYear = emp.employeeCode.split('-')[1];
            return empYear === year;
        });
        
        const sequence = (thisYearEmployees.length + 1).toString().padStart(3, '0');
        return `EMP-${year}-${sequence}`;
    }

    // Add new employee
    async addEmployee(employeeData) {
        try {
            const employeeCode = await this.generateEmployeeCode();
            
            const employee = {
                id: storage.generateId(),
                employeeCode: employeeCode,
                name: employeeData.name.trim(),
                phone: employeeData.phone.trim(),
                email: employeeData.email.trim(),
                position: employeeData.position.trim(),
                department: employeeData.department.trim(),
                hireDate: employeeData.hireDate,
                salary: parseFloat(employeeData.salary) || 0,
                status: 'active',
                permissions: employeeData.permissions || this.getDefaultPermissions(),
                workSchedule: employeeData.workSchedule || this.getDefaultSchedule(),
                notes: employeeData.notes.trim(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await storage.save('employees', employee);
            this.employees.unshift(employee);
            
            this.renderEmployees();
            this.updateStats();
            this.addActivity(`تم إضافة موظف جديد: ${employee.name} - كود: ${employee.employeeCode}`);
            
            return employee;
        } catch (error) {
            console.error('Error adding employee:', error);
            throw error;
        }
    }

    // Update employee
    async updateEmployee(employeeId, updates) {
        try {
            const employee = this.employees.find(e => e.id === employeeId);
            if (!employee) {
                throw new Error('الموظف غير موجود');
            }

            const updatedEmployee = {
                ...employee,
                ...updates,
                updatedAt: new Date().toISOString()
            };

            await storage.save('employees', updatedEmployee);
            
            const index = this.employees.findIndex(e => e.id === employeeId);
            this.employees[index] = updatedEmployee;
            
            this.renderEmployees();
            this.addActivity(`تم تحديث بيانات الموظف: ${updatedEmployee.name}`);
            
            return updatedEmployee;
        } catch (error) {
            console.error('Error updating employee:', error);
            throw error;
        }
    }

    // Delete employee
    async deleteEmployee(employeeId) {
        try {
            const employee = this.employees.find(e => e.id === employeeId);
            if (!employee) {
                throw new Error('الموظف غير موجود');
            }

            // Check if employee has active shifts
            const shifts = await storage.getAll('shifts');
            const employeeShifts = shifts.filter(s => s.employeeName === employee.name && s.status === 'active');
            
            if (employeeShifts.length > 0) {
                throw new Error('لا يمكن حذف موظف لديه شيفتات نشطة');
            }

            await storage.delete('employees', employeeId);
            this.employees = this.employees.filter(e => e.id !== employeeId);
            
            this.renderEmployees();
            this.updateStats();
            this.addActivity(`تم حذف الموظف: ${employee.name}`);
            
        } catch (error) {
            console.error('Error deleting employee:', error);
            throw error;
        }
    }

    // Get default permissions
    getDefaultPermissions() {
        return {
            canOpenShift: false,
            canCloseShift: false,
            canAddCustomers: true,
            canEditPrices: false,
            canViewReports: false,
            canManageEmployees: false,
            canManageProducts: false,
            canDeleteData: false
        };
    }

    // Get default work schedule
    getDefaultSchedule() {
        return {
            sunday: { enabled: true, start: '09:00', end: '17:00' },
            monday: { enabled: true, start: '09:00', end: '17:00' },
            tuesday: { enabled: true, start: '09:00', end: '17:00' },
            wednesday: { enabled: true, start: '09:00', end: '17:00' },
            thursday: { enabled: true, start: '09:00', end: '17:00' },
            friday: { enabled: false, start: '09:00', end: '17:00' },
            saturday: { enabled: false, start: '09:00', end: '17:00' }
        };
    }

    // Get employees for shift selection
    getActiveEmployees() {
        return this.employees.filter(emp => emp.status === 'active');
    }

    // Get employee by code
    getEmployeeByCode(code) {
        return this.employees.find(emp => emp.employeeCode === code);
    }

    // Get employee by name
    getEmployeeByName(name) {
        return this.employees.find(emp => emp.name === name);
    }

    // Render employees list
    renderEmployees() {
        const container = document.getElementById('employeesContainer');
        if (!container) return;

        const filteredEmployees = this.getFilteredEmployees();
        const paginatedEmployees = this.getPaginatedEmployees(filteredEmployees);

        if (paginatedEmployees.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد موظفين</h3>
                    <p>ابدأ بإضافة موظف جديد</p>
                    <button class="btn btn-primary" onclick="employeeManager.showAddEmployeeModal()">
                        <i class="fas fa-plus"></i> إضافة موظف
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="employees-grid">
                ${paginatedEmployees.map(employee => this.createEmployeeCard(employee)).join('')}
            </div>
        `;

        this.renderPagination(filteredEmployees.length);
    }

    // Create employee card HTML
    createEmployeeCard(employee) {
        const statusClass = employee.status === 'active' ? 'status-active' : 'status-inactive';
        const statusText = employee.status === 'active' ? 'نشط' : 'غير نشط';
        
        return `
            <div class="employee-card fade-in">
                <div class="card-header">
                    <div class="employee-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="employee-info">
                        <h3 class="employee-name">${employee.name}</h3>
                        <div class="employee-code">${employee.employeeCode}</div>
                        <div class="employee-position">${employee.position} - ${employee.department}</div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="employeeManager.showEmployeeDetails('${employee.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="employeeManager.showEditEmployeeModal('${employee.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="employeeManager.confirmDeleteEmployee('${employee.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="employee-details">
                        <div class="detail-item">
                            <span class="detail-label">الحالة:</span>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </div>
                        ${employee.phone ? `
                            <div class="detail-item">
                                <span class="detail-label">الهاتف:</span>
                                <span class="detail-value">${employee.phone}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <span class="detail-label">تاريخ التوظيف:</span>
                            <span class="detail-value">${this.formatDate(employee.hireDate)}</span>
                        </div>
                        ${employee.salary > 0 ? `
                            <div class="detail-item">
                                <span class="detail-label">الراتب:</span>
                                <span class="detail-value">${employee.salary.toFixed(2)} جنيه</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="employee-permissions">
                        <h4>الصلاحيات:</h4>
                        <div class="permissions-grid">
                            ${employee.permissions.canOpenShift ? '<span class="permission-badge">فتح شيفت</span>' : ''}
                            ${employee.permissions.canCloseShift ? '<span class="permission-badge">إغلاق شيفت</span>' : ''}
                            ${employee.permissions.canViewReports ? '<span class="permission-badge">التقارير</span>' : ''}
                            ${employee.permissions.canManageEmployees ? '<span class="permission-badge">إدارة الموظفين</span>' : ''}
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button class="btn btn-sm btn-primary" onclick="employeeManager.assignToShift('${employee.id}')">
                        <i class="fas fa-clock"></i> تعيين في شيفت
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="employeeManager.viewEmployeeShifts('${employee.id}')">
                        <i class="fas fa-history"></i> تاريخ الشيفتات
                    </button>
                </div>
            </div>
        `;
    }

    // Add activity log
    addActivity(message) {
        if (window.app && window.app.addActivity) {
            window.app.addActivity(message);
        }
    }

    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }

    // Format datetime
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG');
    }

    // Get filtered employees
    getFilteredEmployees() {
        let filtered = this.employees;

        // Apply search filter
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(emp =>
                emp.name.toLowerCase().includes(term) ||
                emp.employeeCode.toLowerCase().includes(term) ||
                emp.position.toLowerCase().includes(term) ||
                emp.department.toLowerCase().includes(term) ||
                emp.phone.includes(term)
            );
        }

        // Apply status filter
        if (this.filterStatus !== 'all') {
            filtered = filtered.filter(emp => emp.status === this.filterStatus);
        }

        return filtered;
    }

    // Get paginated employees
    getPaginatedEmployees(employees) {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return employees.slice(startIndex, endIndex);
    }

    // Render pagination
    renderPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        const paginationContainer = document.getElementById('employeesPagination');

        if (!paginationContainer || totalPages <= 1) {
            if (paginationContainer) paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="employeeManager.goToPage(${this.currentPage - 1})">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const activeClass = i === this.currentPage ? 'btn-primary' : 'btn-secondary';
            paginationHTML += `<button class="btn btn-sm ${activeClass}" onclick="employeeManager.goToPage(${i})">${i}</button>`;
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="btn btn-sm btn-secondary" onclick="employeeManager.goToPage(${this.currentPage + 1})">التالي</button>`;
        }

        paginationHTML += '</div>';
        paginationContainer.innerHTML = paginationHTML;
    }

    // Go to page
    goToPage(page) {
        this.currentPage = page;
        this.renderEmployees();
    }

    // Update statistics
    updateStats() {
        const totalEmployees = this.employees.length;
        const activeEmployees = this.employees.filter(emp => emp.status === 'active').length;
        const inactiveEmployees = totalEmployees - activeEmployees;

        // Update dashboard if elements exist
        const totalElement = document.getElementById('totalEmployees');
        const activeElement = document.getElementById('activeEmployees');
        const inactiveElement = document.getElementById('inactiveEmployees');

        if (totalElement) totalElement.textContent = totalEmployees;
        if (activeElement) activeElement.textContent = activeEmployees;
        if (inactiveElement) inactiveElement.textContent = inactiveEmployees;
    }

    // Setup event listeners
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('employeeSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.currentPage = 1;
                this.renderEmployees();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('employeeStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.currentPage = 1;
                this.renderEmployees();
            });
        }
    }

    // Show add employee modal
    showAddEmployeeModal() {
        const modal = `
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> إضافة موظف جديد</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addEmployeeForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اسم الموظف: *</label>
                                <input type="text" id="employeeName" required>
                            </div>
                            <div class="form-group">
                                <label>رقم الهاتف:</label>
                                <input type="tel" id="employeePhone">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>البريد الإلكتروني:</label>
                                <input type="email" id="employeeEmail">
                            </div>
                            <div class="form-group">
                                <label>المنصب: *</label>
                                <select id="employeePosition" required>
                                    <option value="">اختر المنصب</option>
                                    <option value="مدير">مدير</option>
                                    <option value="مساعد مدير">مساعد مدير</option>
                                    <option value="موظف استقبال">موظف استقبال</option>
                                    <option value="موظف مبيعات">موظف مبيعات</option>
                                    <option value="محاسب">محاسب</option>
                                    <option value="عامل نظافة">عامل نظافة</option>
                                    <option value="أمن">أمن</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>القسم: *</label>
                                <select id="employeeDepartment" required>
                                    <option value="">اختر القسم</option>
                                    <option value="الإدارة">الإدارة</option>
                                    <option value="المبيعات">المبيعات</option>
                                    <option value="المحاسبة">المحاسبة</option>
                                    <option value="خدمة العملاء">خدمة العملاء</option>
                                    <option value="الصيانة">الصيانة</option>
                                    <option value="الأمن">الأمن</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>تاريخ التوظيف: *</label>
                                <input type="date" id="employeeHireDate" value="${new Date().toISOString().split('T')[0]}" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الراتب (جنيه):</label>
                                <input type="number" id="employeeSalary" min="0" step="0.01">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>الصلاحيات:</label>
                            <div class="permissions-grid">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canOpenShift">
                                    <span>فتح شيفت</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canCloseShift">
                                    <span>إغلاق شيفت</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canAddCustomers" checked>
                                    <span>إضافة عملاء</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canEditPrices">
                                    <span>تعديل الأسعار</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canViewReports">
                                    <span>عرض التقارير</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canManageEmployees">
                                    <span>إدارة الموظفين</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canManageProducts">
                                    <span>إدارة المنتجات</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="canDeleteData">
                                    <span>حذف البيانات</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات:</label>
                            <textarea id="employeeNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="employeeManager.saveEmployee()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        `;

        document.getElementById('modalOverlay').innerHTML = modal;
        showModal();
    }

    // Save employee
    async saveEmployee() {
        try {
            const form = document.getElementById('addEmployeeForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const employeeData = {
                name: document.getElementById('employeeName').value,
                phone: document.getElementById('employeePhone').value,
                email: document.getElementById('employeeEmail').value,
                position: document.getElementById('employeePosition').value,
                department: document.getElementById('employeeDepartment').value,
                hireDate: document.getElementById('employeeHireDate').value,
                salary: document.getElementById('employeeSalary').value,
                notes: document.getElementById('employeeNotes').value,
                permissions: {
                    canOpenShift: document.getElementById('canOpenShift').checked,
                    canCloseShift: document.getElementById('canCloseShift').checked,
                    canAddCustomers: document.getElementById('canAddCustomers').checked,
                    canEditPrices: document.getElementById('canEditPrices').checked,
                    canViewReports: document.getElementById('canViewReports').checked,
                    canManageEmployees: document.getElementById('canManageEmployees').checked,
                    canManageProducts: document.getElementById('canManageProducts').checked,
                    canDeleteData: document.getElementById('canDeleteData').checked
                }
            };

            await this.addEmployee(employeeData);
            closeModal();

            // Show success message
            if (window.app && window.app.showNotification) {
                window.app.showNotification('تم إضافة الموظف بنجاح', 'success');
            }

        } catch (error) {
            alert('حدث خطأ أثناء حفظ الموظف: ' + error.message);
        }
    }

    // Confirm delete employee
    confirmDeleteEmployee(employeeId) {
        const employee = this.employees.find(e => e.id === employeeId);
        if (!employee) return;

        if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            this.deleteEmployee(employeeId).catch(error => {
                alert('حدث خطأ أثناء حذف الموظف: ' + error.message);
            });
        }
    }

    // Assign employee to shift
    assignToShift(employeeId) {
        const employee = this.employees.find(e => e.id === employeeId);
        if (!employee) return;

        if (employee.status !== 'active') {
            alert('لا يمكن تعيين موظف غير نشط في شيفت');
            return;
        }

        if (!employee.permissions.canOpenShift) {
            alert('هذا الموظف لا يملك صلاحية فتح الشيفتات');
            return;
        }

        // Switch to shifts section and pre-fill employee name
        if (window.app) {
            window.app.showSection('shifts');

            // Wait for shifts section to load then pre-fill
            setTimeout(() => {
                const employeeNameInput = document.getElementById('shiftEmployeeName');
                if (employeeNameInput) {
                    employeeNameInput.value = employee.name;
                }
            }, 100);
        }
    }

    // View employee shifts history
    async viewEmployeeShifts(employeeId) {
        const employee = this.employees.find(e => e.id === employeeId);
        if (!employee) return;

        try {
            const shifts = await storage.getAll('shifts');
            const employeeShifts = shifts.filter(s => s.employeeName === employee.name)
                                        .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

            const modal = `
                <div class="modal large">
                    <div class="modal-header">
                        <h3><i class="fas fa-history"></i> تاريخ شيفتات ${employee.name}</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${employeeShifts.length === 0 ? `
                            <div class="empty-state">
                                <i class="fas fa-clock"></i>
                                <h4>لا توجد شيفتات</h4>
                                <p>لم يتم تسجيل أي شيفتات لهذا الموظف بعد</p>
                            </div>
                        ` : `
                            <div class="shifts-history">
                                ${employeeShifts.map(shift => `
                                    <div class="shift-history-item">
                                        <div class="shift-header">
                                            <div class="shift-code">${shift.shiftCode || 'بدون كود'}</div>
                                            <div class="shift-status ${shift.status === 'active' ? 'status-active' : 'status-completed'}">${shift.status === 'active' ? 'نشط' : 'مكتمل'}</div>
                                        </div>
                                        <div class="shift-details">
                                            <div class="detail-row">
                                                <span>تاريخ البداية:</span>
                                                <span>${this.formatDateTime(shift.startTime)}</span>
                                            </div>
                                            ${shift.endTime ? `
                                                <div class="detail-row">
                                                    <span>تاريخ النهاية:</span>
                                                    <span>${this.formatDateTime(shift.endTime)}</span>
                                                </div>
                                            ` : ''}
                                            <div class="detail-row">
                                                <span>إجمالي المبيعات:</span>
                                                <span>${shift.totalSales.toFixed(2)} جنيه</span>
                                            </div>
                                            <div class="detail-row">
                                                <span>صافي الأرباح:</span>
                                                <span>${shift.netAmount.toFixed(2)} جنيه</span>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                    </div>
                </div>
            `;

            document.getElementById('modalOverlay').innerHTML = modal;
            showModal();

        } catch (error) {
            alert('حدث خطأ أثناء تحميل تاريخ الشيفتات');
        }
    }
}

// Initialize employee manager
let employeeManager;
document.addEventListener('DOMContentLoaded', () => {
    employeeManager = new EmployeeManager();
});
