@echo off
chcp 65001 >nul
title اختبار نظام إدارة مكان المذاكرة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 اختبار النظام الشامل                     ║
echo ║                   System Comprehensive Test                  ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🔍 فحص جميع مكونات النظام                                    ║
echo ║  ⚡ التأكد من جاهزية التشغيل                                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 بدء الاختبارات الشاملة...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    goto :end
)

echo ✅ Python مثبت

REM فحص وجود ملف الاختبار
if not exist "test_server.py" (
    echo ❌ ملف test_server.py غير موجود
    goto :end
)

echo ✅ ملف الاختبار موجود

echo.
echo 🚀 تشغيل الاختبارات...
echo.

REM تشغيل اختبارات النظام
python test_server.py

echo.
echo 📋 تعليمات ما بعد الاختبار:
echo.
echo   ✅ إذا نجحت جميع الاختبارات:
echo      - شغل start_server.bat لبدء النظام
echo.
echo   ❌ إذا فشلت بعض الاختبارات:
echo      - راجع الأخطاء المذكورة أعلاه
echo      - تأكد من تثبيت Python بشكل صحيح
echo      - تأكد من وجود جميع الملفات
echo.

:end
echo.
echo 👋 انتهى الاختبار
pause
