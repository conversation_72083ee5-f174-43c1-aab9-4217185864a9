@echo off
chcp 65001 >nul
title نظام إدارة مكان المذاكرة - Study Place Management

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎓 نظام إدارة مكان المذاكرة                    ║
echo ║                      Study Place Management                  ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🚀 تشغيل السيرفر المحلي                                       ║
echo ║  💾 حفظ آمن للبيانات                                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص Python...

REM فحص وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح

REM فحص وجود الملفات المطلوبة
echo 🔍 فحص الملفات...

if not exist "index.html" (
    echo ❌ ملف index.html غير موجود
    goto :missing_files
)

if not exist "js\app.js" (
    echo ❌ ملف js\app.js غير موجود
    goto :missing_files
)

if not exist "css\styles.css" (
    echo ❌ ملف css\styles.css غير موجود
    goto :missing_files
)

echo ✅ جميع الملفات موجودة

echo.
echo 🚀 تشغيل السيرفر...
echo.

REM تشغيل السيرفر
python server.py

goto :end

:missing_files
echo.
echo ❌ ملفات مفقودة! تأكد من وجود جميع ملفات النظام
echo.
pause
exit /b 1

:end
echo.
echo 👋 شكراً لاستخدام نظام إدارة مكان المذاكرة
pause
